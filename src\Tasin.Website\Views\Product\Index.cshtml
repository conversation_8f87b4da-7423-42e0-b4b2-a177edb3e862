﻿@{
    ViewData["Title"] = "Hàng hoá";
}

@section Styles {
    <link href="~/css/kendo-grid-common.css" rel="stylesheet" asp-append-version="true" />
    <link href="~/css/toolbar-common.css" rel="stylesheet" asp-append-version="true" />
    <link href="~/css/disabled-fields-common.css" rel="stylesheet" asp-append-version="true" />
}

<div>

    <h4 class="demo-section wide title">@ViewData["Title"]</h4>
    @*   <div class="demo-section wide title">
    <h4 class="demo-section wide title">@ViewData["Title"]</h4>
    <nav id="breadcrumb"></nav>
    </div> *@
    <div id="divContent">
        <div id="gridId">
        </div>
    </div>
    <div id="window"></div>
    <div id="dialog"></div>
</div>

<script type="text/javascript">
    let gridId = "#gridId";
    let formData = {};

    function renderCreateOrEditForm(isCreate = true, dataProduct = {}) {
        let myWindow = $("#window");
        $("#window").html("<form id='formCreateAndEdit'></form>");

        formData = {
            id: 0,
            name: "",
            name_EN: "",
            unit_ID: null,
            category_ID: null,
            processingType_ID: null,
            companyTaxRate: null,
            consumerTaxRate: null,
            specialProductTaxRate_ID: null,
            lossRate: null,
            processingType: "Material",
            additionalCost: null,
            note: null,
            isDiscontinued: false,
            processingFee: null,
            parentID: null,
            // status: "",
            ...dataProduct
        };

        // Ensure all dropdown IDs are always numbers or null, not objects
        if (formData.parentID && typeof formData.parentID === 'object') {
            formData.parentID = formData.parentID.id || formData.parentID.Id || formData.parentID.ID || null;
        }
        if (formData.unit_ID && typeof formData.unit_ID === 'object') {
            formData.unit_ID = formData.unit_ID.id || formData.unit_ID.Id || formData.unit_ID.ID || null;
        }
        if (formData.category_ID && typeof formData.category_ID === 'object') {
            formData.category_ID = formData.category_ID.id || formData.category_ID.Id || formData.category_ID.ID || null;
        }
        if (formData.processingType_ID && typeof formData.processingType_ID === 'object') {
            formData.processingType_ID = formData.processingType_ID.id || formData.processingType_ID.Id || formData.processingType_ID.ID || null;
        }
        if (formData.specialProductTaxRate_ID && typeof formData.specialProductTaxRate_ID === 'object') {
            formData.specialProductTaxRate_ID = formData.specialProductTaxRate_ID.id || formData.specialProductTaxRate_ID.Id || formData.specialProductTaxRate_ID.ID || null;
        }

        let strSubmit = "Thêm";
        let title = "THÊM MỚI"
        let element;
        if (isCreate == false) {
            strSubmit = "Sửa";
            title = "CẬP NHẬT";
        }
        $("#formCreateAndEdit").kendoForm({
            layout: "grid",
            grid: {
                cols: 2,
                gutter: "1rem"
            },
            formData: formData,
            type: "group",
            items: [
                // ===== NHÓM 1: THÔNG TIN CỞ BẢN =====
                {
                    field: "code",
                    title: "Mã hàng hoá",
                    label: "Mã hàng hoá:",
                    colSpan: 1,
                    attributes: {
                        placeholder: "Mã sẽ được sinh tự động...",
                        disabled: true,
                        readonly: true
                    }
                },
                {
                    field: "name",
                    title: "Hàng hoá",
                    label: "Hàng hoá (*):",
                    colSpan: 1,
                    validation: {
                        validationMessage: "Vui lòng nhập hàng hoá",
                        required: true
                    },
                },
                {
                    field: "name_EN",
                    title: "Hàng hoá tiếng anh",
                    label: "Hàng hoá tiếng anh:",
                    colSpan: 1,
                },
                {
                    field: "unit_ID",
                    title: "Đơn vị",
                    label: "Đơn vị (*):",
                    colSpan: 1,
                    editor: "DropDownList",
                    validation: {
                        validationMessage: "Vui lòng chọn đơn vị",
                        required: true
                    },
                    editorOptions: {
                        optionLabel: "Chọn đơn vị",
                        dataTextField: "name",
                        dataValueField: "id",
                        valuePrimitive: true,
                        filter: filterCustom,
                        dataSource: {
                            transport: {
                                read: {
                                    url: "/Unit/GetUnitList",
                                    dataType: "json",
                                },
                                parameterMap: function (data, type) {
                                    if (type == "read") {
                                        return {
                                            pageSize: data.pageSize,
                                            pageNumber: data.page,
                                        }
                                    }
                                },
                            },
                            serverPaging: true,
                            serverFiltering: true,
                            page: 1,
                            pageSize: 9999,
                            schema: {
                                data: "data.data",
                                total: "data.total"
                            },
                        },
                        change: function (e) {
                            formData.unit_ID = this.value();
                        }
                    },
                },
                {
                    field: "category_ID",
                    title: "Quy cách",
                    label: "Quy cách:",
                    colSpan: 1,
                    editor: "DropDownList",
                    editorOptions: {
                        optionLabel: "Chọn quy cách",
                        dataTextField: "name",
                        dataValueField: "id",
                        valuePrimitive: true,
                        filter: filterCustom,
                        dataSource: {
                            transport: {
                                read: {
                                    url: "/Category/GetCategoryList",
                                    dataType: "json",
                                },
                                parameterMap: function (data, type) {
                                    if (type == "read") {
                                        return {
                                            pageSize: data.pageSize,
                                            pageNumber: data.page,
                                        }
                                    }
                                },
                            },
                            serverPaging: true,
                            serverFiltering: true,
                            page: 1,
                            pageSize: 9999,
                            schema: {
                                data: "data.data",
                                total: "data.total"
                            },
                        },
                        change: function (e) {
                            formData.category_ID = this.value();
                        }
                    },
                },

                {
                    field: "specialProductTaxRate_ID",
                    title: "Loại mặt hàng",
                    label: "Loại mặt hàng:",
                    colSpan: 1,
                    editor: "DropDownList",
                    editorOptions: {
                        optionLabel: "Chọn loại mặt hàng",
                        dataTextField: "name",
                        dataValueField: "id",
                        valuePrimitive: true,
                        filter: filterCustom,
                        dataSource: {
                            transport: {
                                read: {
                                    url: "/SpecialProductTaxRate/GetSpecialProductTaxRateList",
                                    dataType: "json",
                                },
                                parameterMap: function (data, type) {
                                    if (type == "read") {
                                        return {
                                            pageSize: data.pageSize,
                                            pageNumber: data.page,
                                        }
                                    }
                                },
                            },
                            serverPaging: true,
                            serverFiltering: true,
                            page: 1,
                            pageSize: 9999,
                            schema: {
                                data: "data.data",
                                total: "data.total",
                                model: {
                                    id: "id",
                                    fields: {
                                        id: { type: "number" },
                                        name: { type: "string" }
                                    }
                                }
                            },
                        },
                        change: function (e) {
                            formData.specialProductTaxRate_ID = this.value();
                        }
                    },
                },

                // ===== NHÓM 2: PHÂN LOẠI VÀ THUẾ =====

                {
                    field: "companyTaxRate",
                    title: "Mức thuế doanh nghiệp",
                    label: "Mức thuế doanh nghiệp:",
                    colSpan: 1,
                    attributes: {
                        type: "number",
                        step: "0.01",
                        min: "0"
                    }
                },
                {
                    field: "consumerTaxRate",
                    title: "Mức thuế người tiêu dùng",
                    label: "Mức thuế người tiêu dùng:",
                    colSpan: 1,
                    attributes: {
                        type: "number",
                        step: "0.01",
                        min: "0"
                    }
                },

                // ===== NHÓM 3: GIÁ VÀ CHI PHÍ =====
                {
                    field: "defaultPrice",
                    title: "Giá mặc định",
                    label: "Giá mặc định (*):",
                    colSpan: 1,
                    editor:"NumericTextBox",
                    editorOptions:{
                        format: "n0",
                        decimals: 2,
                        min: 0, 
                    },
                    validation: {
                        validationMessage: "Vui lòng nhập giá mặc định",
                        required: true
                    },
                },
                {
                    field: "processingType",
                    title: "Phân loại hàng hoá",
                    label: "Phân loại hàng hoá:",
                    colSpan: 1,
                    editor: "DropDownList",
                    editorOptions: {
                        optionLabel: "Chọn phân loại hàng hoá",
                        dataTextField: "text",
                        dataValueField: "value",
                        valuePrimitive: true,
                        filter: filterCustom,
                        dataSource: {
                            transport: {
                                read: {
                                    url: "/Common/GetDataOptionsDropdown",
                                    dataType: "json",
                                },
                                parameterMap: function (data, type) {
                                    if (type == "read") {
                                        return {
                                            searchString: data.filter?.filters?.[0]?.value || "",
                                            type: "ProcessingType" // ECategoryType.ProcessingType
                                        }
                                    }
                                },
                            },
                            serverFiltering: true,
                            schema: {
                                data: "data"
                            },
                        },
                        change: function (e) {
                            formData.processingType = this.value();

                            // Handle field enabling/disabling based on ProcessingType
                            var isMaterial = this.value() === "Material";

                            // Get form instance
                            var form = $("#formCreateAndEdit").data("kendoForm");
                            if (form) {
                                // Disable/enable and reset fields when Material is selected
                                if (isMaterial) {
                                    // Reset values to null/empty
                                    formData.processingFee = null;
                                    formData.lossRate = null;
                                    formData.parentID = null;
                                    formData.additionalCost = null;

                                    // Update form fields
                                    form.editable.options.model.set("processingFee", null);
                                    form.editable.options.model.set("lossRate", null);
                                    form.editable.options.model.set("parentID", null);
                                    form.editable.options.model.set("additionalCost", null);
                                }

                                // Enable/disable form fields
                                var processingFeeField = form.element.find("[name='processingFee']");
                                var lossRateField = form.element.find("[name='lossRate']");
                                var parentIDField = form.element.find("[name='parentID']");
                                var additionalCostField = form.element.find("[name='additionalCost']");

                                if (isMaterial) {
                                    processingFeeField.prop("disabled", true).addClass("k-disabled field-disabled");
                                    lossRateField.prop("disabled", true).addClass("k-disabled field-disabled");
                                    additionalCostField.prop("disabled", true).addClass("k-disabled field-disabled");

                                    // Handle dropdown for parentID
                                    var parentDropdown = parentIDField.data("kendoDropDownList");
                                    if (parentDropdown) {
                                        parentDropdown.enable(false);
                                        parentDropdown.wrapper.addClass("field-disabled");
                                        parentDropdown.value("");
                                    }
                                } else {
                                    processingFeeField.prop("disabled", false).removeClass("k-disabled field-disabled");
                                    lossRateField.prop("disabled", false).removeClass("k-disabled field-disabled");
                                    additionalCostField.prop("disabled", false).removeClass("k-disabled field-disabled");

                                    // Handle dropdown for parentID
                                    var parentDropdown = parentIDField.data("kendoDropDownList");
                                    if (parentDropdown) {
                                        parentDropdown.enable(true);
                                        parentDropdown.wrapper.removeClass("field-disabled");
                                    }
                                }
                            }
                        }
                    },
                },

                {
                    field: "parentID",
                    title: "Nguyên liệu chính",
                    label: "Nguyên liệu chính:",
                    colSpan: 1,
                    editor: "DropDownList",
                    editorOptions: {
                        optionLabel: "Chọn nguyên liệu chính",
                        dataTextField: "name",
                        dataValueField: "id",
                        valuePrimitive: true, // 🔑 Bắt buộc để giá trị là ID thay vì object
                        filter: filterCustom,
                        dataSource: {
                            transport: {
                                read: {
                                    url: "/Product/GetProductList",
                                    datatype: "json",
                                },
                                parameterMap: function (data, type) {
                                    if (type == "read") {
                                        return {
                                            pageSize: 100,
                                            pageNumber: 1,
                                            includeDiscontinued: false,
                                            excludeProductId: formData.id > 0 ? formData.id : null
                                        };
                                    }
                                },
                            },
                            serverPaging: true,
                            serverFiltering: true,
                            pageSize: 100,
                            schema: {
                                type: 'json',
                                parse: function (response) {
                                    if (response.isSuccess === false) {
                                        showErrorMessages(response.errorMessageList);
                                        return { data: [], total: 0 };
                                    }
                                    return response.data;
                                },
                                model: {
                                    id: "id",
                                    fields: {
                                        name: { type: "string" },
                                        code: { type: "string" }
                                    }
                                },
                                data: "data",
                                total: "total"
                            },
                        },
                        change: function (e) {
                            formData.parentID = this.value(); // đơn giản chỉ cần ID
                        }
                    }
                },
                {
                    field: "lossRate",
                    title: "Hao hụt (%)",
                    label: "Hao hụt (%):",
                    colSpan: 1,
                    attributes: {
                        type: "number",
                        step: "0.01",
                        min: "0"
                    }
                },
                {
                    field: "processingFee",
                    title: "Mức phí gia công",
                    label: "Mức phí gia công:",
                    colSpan: 1,
                    attributes: {
                        type: "number",
                        step: "0.01",
                        min: "0"
                    }
                },

                {
                    field: "additionalCost",
                    title: "Chi phí thêm",
                    label: "Chi phí thêm:",
                    colSpan: 1,
                    attributes: {
                        type: "number",
                        step: "0.01",
                        min: "0"
                    },
                    validation: {
                        validationMessage: "Vui lòng nhập đơn giá",
                        required: true
                    },
                },

                // ===== NHÓM 4: THUỘC TÍNH SẢN PHẨM =====
                {
                    field: "isDiscontinued",
                    title: "Ngừng sử dụng",
                    label: "Ngừng sử dụng:",
                    colSpan: 1,
                    editor: "CheckBox",
                },
                {
                    field: "note",
                    title: "Ghi chú",
                    label: "Ghi chú:",
                    colSpan: 2,
                    editor: "TextArea",
                    editorOptions: {
                        rows: 3,
                        placeholder: "Nhập ghi chú..."
                    }
                },
            ],
            messages: {
                submit: strSubmit, clear: "Đặt lại"
            },
            formData: formData,
            // Initialize field states after form is rendered
            init: function (e) {
                // Apply initial field state based on ProcessingType
                setTimeout(function () {
                    var isMaterial = formData.processingType === "Material";
                    var form = $("#formCreateAndEdit").data("kendoForm");

                    if (form) {
                        // Get all the fields
                        var processingFeeField = form.element.find("[name='processingFee']");
                        var lossRateField = form.element.find("[name='lossRate']");
                        var parentIDField = form.element.find("[name='parentID']");
                        var additionalCostField = form.element.find("[name='additionalCost']");

                        if (isMaterial) {
                            // Disable fields for Material type
                            processingFeeField.prop("disabled", true).addClass("k-disabled field-disabled");
                            lossRateField.prop("disabled", true).addClass("k-disabled field-disabled");
                            additionalCostField.prop("disabled", true).addClass("k-disabled field-disabled");

                            // Handle dropdown for parentID
                            var parentDropdown = parentIDField.data("kendoDropDownList");
                            if (parentDropdown) {
                                parentDropdown.enable(false);
                                parentDropdown.wrapper.addClass("field-disabled");
                            }

                        } else {
                            // Ensure fields are enabled for non-Material types
                            processingFeeField.prop("disabled", false).removeClass("k-disabled field-disabled");
                            lossRateField.prop("disabled", false).removeClass("k-disabled field-disabled");
                            additionalCostField.prop("disabled", false).removeClass("k-disabled field-disabled");

                            // Handle dropdown for parentID
                            var parentDropdown = parentIDField.data("kendoDropDownList");
                            if (parentDropdown) {
                                parentDropdown.enable(true);
                                parentDropdown.wrapper.removeClass("field-disabled");
                            }
                        }

                        // Add price calculator button to defaultPrice field
                        addPriceCalculatorButton();
                    }
                }, 300); // Increased delay to ensure form is fully rendered
            },
            submit: function (e) {
                e.preventDefault();

                // Validate form before submitting
                var form = $("#formCreateAndEdit").data("kendoForm");
                if (!form.validate()) {
                    kendo.alert("Vui lòng điền đầy đủ thông tin bắt buộc!");
                    return;
                }

                let dataItem = {
                    ...formData,
                    ...e.model,
                };
                if (dataItem.id > 0) {
                    var response = ajax("PUT", "/Product/UpdateProduct/" + dataItem.id, dataItem, () => {
                        $(gridId).data("kendoGrid").dataSource.filter({});
                        myWindow.data("kendoWindow").close();
                    });
                }
                else {
                    var response = ajax("POST", "/Product/Create", dataItem, () => {
                        $(gridId).data("kendoGrid").dataSource.filter({});
                        myWindow.data("kendoWindow").close();
                    });
                }
            },
            close: function (e) {
                $(this.element).empty();
            },
        });
        if (!isCreate) {

        }

        // if (Userdata.roleIdList?.includes(ERoleType.Admin) == false) {
        //     $("#userName").data("kendoTextBox").enable(false);
        // }


        setTimeout(() => {
            $("input[title='name']").focus();

            // Additional check to ensure fields are disabled if ProcessingType is Material
            if (formData.processingType === "Material") {
                var form = $("#formCreateAndEdit").data("kendoForm");
                if (form) {
                    var processingFeeField = form.element.find("[name='processingFee']");
                    var lossRateField = form.element.find("[name='lossRate']");
                    var parentIDField = form.element.find("[name='parentID']");
                    var additionalCostField = form.element.find("[name='additionalCost']");

                    processingFeeField.prop("disabled", true).addClass("k-disabled field-disabled");
                    lossRateField.prop("disabled", true).addClass("k-disabled field-disabled");
                    additionalCostField.prop("disabled", true).addClass("k-disabled field-disabled");

                    // Handle dropdown for parentID
                    var parentDropdown = parentIDField.data("kendoDropDownList");
                    if (parentDropdown) {
                        parentDropdown.enable(false);
                        parentDropdown.wrapper.addClass("field-disabled");
                    }
                }
            }

            // Add price calculator button to defaultPrice field
            addPriceCalculatorButton();
        }, 500);

        function remove() {
            setTimeout(() => {
                if ($(".k-window #window").length > 0) {
                    $("#window").parent().remove();
                    $(gridId).after("<div id='window'></div>");
                }
            }, 200)
        }

        myWindow.kendoWindow({
            width: "800px",
            // height: "50vh",
            title: "",
            visible: false,
            actions: ["Close"],
            resizable: false,
            draggable: false,
            modal: true,
            close: function (e) {
                //$("#window").empty();
                remove();
            },
        }).data("kendoWindow").title(title).center();
        myWindow.data("kendoWindow").open();

    }

    async function editProduct(id) {
        var response = ajax("GET", "/Product/GetProductById/" + id, {}, (response) => {
            renderCreateOrEditForm(false, response.data);
        }, null, false);
    }
    function deleteProduct(id) {
        $('#dialog').kendoConfirm({
            title: "THÔNG BÁO XÓA HÀNG HOÁ",
            content: "Bạn có chắc chắn xóa hàng hoá này không?",
            size: "medium",
            messages: {
                okText: "Đồng ý",
                cancel: "Hủy"

            },
        }).data("kendoConfirm").open().result.done(function () {
            var response = ajax("DELETE", "/Product/DeleteProductById/" + id, {}, () => {
                $(gridId).data("kendoGrid").dataSource.filter({});
            });
        })

        $("#window").after("<div id='dialog'></div>");
    }



    async function ExportExcel() {
        let dataSheet1 = [
            {
                cells: [
                    { value: "Mã hàng hoá", textAlign: "center", background: "#428dd8" },
                    { value: "Hàng hoá", textAlign: "center", background: "#428dd8" },
                    { value: "Hàng hoá tiếng anh", textAlign: "center", background: "#428dd8" },
                    { value: "Đơn vị", textAlign: "center", background: "#428dd8" },
                    { value: "Quy cách", textAlign: "center", background: "#428dd8" },
                    { value: "Loại mặt hàng", textAlign: "center", background: "#428dd8" },
                    { value: "Giá bán", textAlign: "center", background: "#428dd8" },
                    { value: "Thuế doanh nghiệp", textAlign: "center", background: "#428dd8" },
                    { value: "Thuế người tiêu dùng", textAlign: "center", background: "#428dd8" },

                    { value: "Phân loại hàng hoá", textAlign: "center", background: "#428dd8" },
                    { value: "Nguyên liệu chính", textAlign: "center", background: "#428dd8" },
                    { value: "Hao hụt", textAlign: "center", background: "#428dd8" },
                    { value: "Phí gia công", textAlign: "center", background: "#428dd8" },
                    { value: "Chi phí thêm", textAlign: "center", background: "#428dd8" },
                    { value: "Ngừng sử dụng", textAlign: "center", background: "#428dd8" },

                    { value: "Ghi chú", textAlign: "center", background: "#428dd8" },
                    { value: "Ngày cập nhật", textAlign: "center", background: "#428dd8" },
                    { value: "Người cập nhật", textAlign: "center", background: "#428dd8" },
                ]
            }];

        var searchModel = getSearchModel();
        let postData = {
            ...searchModel,
            pageSize: 999999999,
            pageNumber: 1
        }
        let dataSourceProduct = null;
        var response = await ajax("GET", "/Product/GetProductList", postData, (urnResponse) => {
            dataSourceProduct = urnResponse.data.data;
        }, null, false);
        if (dataSourceProduct == null) return;

        for (let index = 0; index < dataSourceProduct.length; index++) {
            dataSheet1.push({
                cells: [
                    { value: dataSourceProduct[index].code },
                    { value: dataSourceProduct[index].name },
                    { value: dataSourceProduct[index].name_EN },

                    { value: dataSourceProduct[index].unitName },
                    { value: dataSourceProduct[index].categoryName },
                    { value: dataSourceProduct[index].specialProductTaxRateName },
                    { value: dataSourceProduct[index].defaultPrice != null ? kendo.toString(dataSourceProduct[index].defaultPrice, "n0") : ""},
                    { value: dataSourceProduct[index].companyTaxRate != null ? kendo.toString(dataSourceProduct[index].companyTaxRate, "n0") : "" },
                    { value: dataSourceProduct[index].consumerTaxRate != null ? kendo.toString(dataSourceProduct[index].consumerTaxRate, "n0") : "" },
                    { value: dataSourceProduct[index].processingTypeName },
                    { value: dataSourceProduct[index].parentName },
                    { value: dataSourceProduct[index].lossRate != null ? kendo.toString(dataSourceProduct[index].lossRate, "n2") + "%" : "" },
                    { value: dataSourceProduct[index].processingFee != null ? kendo.toString(dataSourceProduct[index].processingFee, "n0") : ""},
                    { value: dataSourceProduct[index].additionalCost != null ? kendo.toString(dataSourceProduct[index].additionalCost, "n0") : "" },
                    { value: dataSourceProduct[index].isDiscontinued ? "Y" : "N" },

                    { value: dataSourceProduct[index].note },
                    { value: kendo.toString(kendo.parseDate(dataSourceProduct[index].updatedDate), "dd/MM/yyyy") },
                    { value: dataSourceProduct[index].updatedByName },
                ]
            })
        }

        var workbook = new kendo.ooxml.Workbook({
            sheets: [
                {
                    name: "Danh sách hàng hoá",
                    columns: [
                        { width: 150 }, { width: 200 }, { width: 200 },
                        { width: 250 }, { autoWidth: true }, { autoWidth: true }, { autoWidth: true }, { autoWidth: true },
                        { autoWidth: true }, { autoWidth: true },{ autoWidth: true }, { autoWidth: true }, { autoWidth: true }, 
                        { autoWidth: true },{ autoWidth: true }, { width: 150 }, { width: 150 }, { width: 150 }
                    ],
                    rows: dataSheet1,
                }
            ]
        });
        kendo.saveAs({
            dataURI: workbook.toDataURL(),
            fileName: "Danh sách hàng hoá _ " + kendo.toString(new Date(), "dd_MM_yyyy__HH_mm_ss") + ".xlsx"
        });
    }

    function getSearchModel() {
        let searchString = $("#searchString").val();

        return {
            searchString,
        };
    }
    function InitGrid() {
        let htmlToolbar = `
                <div id='toolbar' style=''  class='w-100 d-flex flex-column'>
                       <div class="row gx-0 row-gap-2 w-100">
                            <div class="col-xl-3 col-lg-4 col-md-6 col-sm-6 col-12">
                                <div class="pe-1">
                                    <label for="searchString">Tìm kiếm:</label>
                                    <input type="text" class="w-100" id="searchString"/>
                                </div>
                            </div>
                            <div class="col-xl-3 col-lg-4 col-md-6 col-sm-6 col-12 d-flex align-items-end">
                                <div class="pe-1 d-flex gap-2">
                                    <button id="search" title="Tìm kiếm" class="k-button k-button-md k-rounded-md k-button-solid k-button-solid-primary k-icon-button"><span class='k-icon k-i-search k-button-icon'></span><span class='k-button-text d-none'>Tìm kiếm</span></button>
                                    <button id='create' title="Thêm" class='k-button k-button-md k-rounded-md k-button-solid k-button-solid-success _permission_' data-enum='12'><span class='k-icon k-i-plus k-button-icon'></span><span class='k-button-text'>Thêm</span></button>
                                    <button id="downloadTemplate" title="Tải file mẫu import sản phẩm" class="k-button k-button-md k-rounded-md k-button-outline k-button-outline-info"><span class="k-icon k-i-download k-button-icon"></span><span class="k-button-text">Tải file mẫu</span></button>
                                    <button id="importExcel" title="Import danh sách sản phẩm từ Excel" class="k-button k-button-md k-rounded-md k-button-solid k-button-solid-info"><span class="k-icon k-i-upload k-button-icon"></span><span class="k-button-text">Import Excel</span></button>
                                    <button id="exportExcel" class="k-button k-button-md k-rounded-md k-button-outline k-button-outline-error"><span class="k-icon k-i-file-excel k-button-icon"></span><span class="k-button-text">Export Excel</span></button>
                                </div>
                            </div>
                        </div>

                </div>
            `;

        $(gridId).kendoGrid({
            dataSource: {
                transport: {
                    read: {
                        url: "/Product/GetProductList",
                        datatype: "json",
                    },
                    parameterMap: function (data, type) {
                        if (type == "read") {
                            var searchModel = getSearchModel();
                            return {
                                ...searchModel,
                                pageSize: data.pageSize,
                                pageNumber: data.page
                            }
                        }

                    },
                },
                serverPaging: true,
                serverFiltering: true,
                page: 1,
                pageSize: 20,
                schema: {
                    type: 'json',
                    parse: function (response) {
                        if (response.isSuccess == false) {
                            showErrorMessages(response.errorMessageList);
                            return {
                                data: [],
                                total: 0
                            }
                        }
                        return response.data;
                    },
                    model: {
                        id: "id",
                        fields: {
                            createdDate: { type: "date" },
                            updatedDate: { type: "date" },
                            stt: { type: "number" },

                        }
                    },
                    data: "data",
                    total: "total"
                },
            },
            selectable: true,
            scrollable: { virtual: false },
            pageable: {
                pageSizes: [10, 20, 50],
            },
            dataBinding: function (e) {
                record = (this.dataSource._page - 1) * this.dataSource._pageSize;
            },
            toolbar: htmlToolbar,
            // toolbar: "<div id='toolbar' style='width:100%'></div><div class='report-toolbar'>\</div>",
            columns: [
                {
                    field: "",
                    title: "STT",
                    headerAttributes: { style: "text-align: center; justify-content: center" },
                    attributes: { style: "text-align:center;" },
                    template: "#: ++record #",
                    width: "80px"
                },
                {
                    field: "code",
                    title: "Mã hàng hoá",
                    headerAttributes: { style: "text-align: center; justify-content: center" },
                    attributes: { style: "text-align:center;" },
                    width: "150px",
                },
                {
                    field: "name",
                    title: "Hàng hoá",
                    headerAttributes: { style: "text-align: center; justify-content: center" },
                    attributes: { style: "text-align:center;" },
                    width: "200px",
                },
                {
                    field: "name_EN",
                    title: "Hàng hoá tiếng anh",
                    headerAttributes: { style: "text-align: center; justify-content: center" },
                    attributes: { style: "text-align:center;" },
                    width: "180px",
                },
                {
                    field: "processingTypeName",
                    title: "Phân loại hàng hoá",
                    headerAttributes: { style: "text-align: center; justify-content: center" },
                    attributes: { style: "text-align:center;" },
                    width: "150px",
                },
                {
                    field: "note",
                    title: "Ghi chú",
                    width: "250px",
                    headerAttributes: { style: "text-align: center; justify-content: center" },
                    attributes: { style: "text-align:left;" },
                },
                {
                    field: "updatedDate",
                    title: "Ngày cập nhật",
                    width: "150px",
                    headerAttributes: { style: "text-align: center; justify-content: center" },
                    attributes: { style: "text-align:center;" },
                    template: '#: kendo.toString(kendo.parseDate(updatedDate || createdDate), "dd/MM/yyyy HH:mm:ss")#',
                },
                {
                    field: "updatedByName",
                    title: "Người cập nhật",
                    width: "150px",
                    headerAttributes: { style: "text-align: center; justify-content: center" },
                    attributes: { style: "text-align:center;" },
                },
                {
                    field: "", title: "Thao tác", width: "120px", attributes: { style: "text-align: center;" },
                    headerAttributes: { style: "text-align: center; justify-content: center" },
                    template: function (dataItem) {
                        return '<div class="action-buttons">' +
                            '<button onclick="editProduct(' + dataItem.id + ')" title="Chỉnh sửa" class="btn-action btn-edit _permission_" data-enum="14">' +
                            '<i class="fas fa-edit"></i>' +
                            '</button>' +
                            '<button onclick="deleteProduct(' + dataItem.id + ')" title="Xoá" class="btn-action btn-delete _permission_" data-enum="15">' +
                            '<i class="fas fa-trash"></i>' +
                            '</button>' +
                            '</div>';
                    }
                }
            ],
            dataBound: function (e) {
                CheckPermission();
            }
        });


    }
    function InitKendoToolBar() {

        $("#search").kendoButton({
            icon: "search"
        });
        $("#search").click(async function (e) {
            var grid = $(gridId).data("kendoGrid");
            grid.dataSource.filter({});
        });
        $("#exportExcel").click(async function (e) {
            ExportExcel();
        });

        $("#downloadTemplate").click(async function (e) {
            downloadTemplate();
        });

        $("#importExcel").click(async function (e) {
            showImportDialog();
        });

        $("#searchString").kendoTextBox({
            icon: {
                type: "search",
                position: "end"  // Có thể là "start" hoặc "end"
            },
            placeholder: "Nhập từ khóa tìm kiếm..."
        });
        $("#create").kendoButton({
            icon: "plus"
        });

        $("#export").click(async function (e) {
            let grid = $(gridId).data("kendoGrid");
            grid.saveAsExcel();
        });


        $("#create").on('click', function () {
            renderCreateOrEditForm();
        });

    };

    // Download Excel template function
    async function downloadTemplate() {
        try {
            const response = await fetch('/Product/DownloadTemplate');
            if (response.ok) {
                const blob = await response.blob();
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `Product_Import_Template_${new Date().toISOString().slice(0, 10)}.xlsx`;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                window.URL.revokeObjectURL(url);

                kendo.ui.progress($(document.body), false);
                notification.show({
                    title: "Thành công!",
                    message: "Đã tải xuống file mẫu thành công!"
                }, "success");
            } else {
                throw new Error('Không thể tải xuống file mẫu');
            }
        } catch (error) {
            console.error('Error downloading template:', error);
            notification.show({
                title: "Lỗi!",
                message: "Lỗi khi tải xuống file mẫu: " + error.message
            }, "error");
        }
    }

    // Show import dialog function
    function showImportDialog() {
        const dialogHtml = `
            <div id="importDialog">
                <div class="k-form-layout" style="grid-template-columns: 1fr;">
                    <div class="k-form-field">
                        <label class="k-label k-form-label">Chọn file Excel để import:</label>
                        <div class="k-form-field-wrap">
                            <input type="file" id="importFile" accept=".xlsx,.xls,.xlsm,.csv" class="k-textbox" />
                            <div class="k-form-hint">Chỉ chấp nhận file Excel (.xlsx, .xls, .xlsm) hoặc CSV</div>
                        </div>
                    </div>
                    <div class="k-form-field">
                        <div class="k-form-field-wrap">
                            <div id="importProgress" style="display: none;">
                                <div class="k-progressbar">
                                    <div class="k-progress-status-wrap">
                                        <span class="k-progress-status">Đang xử lý...</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;

        const dialog = $(dialogHtml).kendoDialog({
            width: "500px",
            title: "Import sản phẩm từ Excel",
            closable: true,
            modal: true,
            actions: [
                {
                    text: "Hủy",
                    action: function (e) {
                        return true; // Close dialog
                    }
                },
                {
                    text: "Import",
                    primary: true,
                    action: function (e) {
                        const fileInput = document.getElementById('importFile');
                        if (fileInput.files.length === 0) {
                            notification.show({
                                title: "Cảnh báo!",
                                message: "Vui lòng chọn file để import!"
                            }, "info");
                            return false; // Don't close dialog
                        }

                        importExcelFile(fileInput.files[0]);
                        return false; // Don't close dialog yet
                    }
                }
            ]
        }).data("kendoDialog");

        dialog.open();
    }

    // Import Excel file function
    async function importExcelFile(file) {
        try {
            $("#importProgress").show();

            const formData = new FormData();
            formData.append('file', file);

            const response = await fetch('/Product/ImportExcel', {
                method: 'POST',
                body: formData
            });

            const result = await response.json();
            $("#importProgress").hide();

            if (result.isSuccess && result.data) {
                showImportResult(result.data);

                // Refresh grid if there were successful imports
                if (result.data.successfulRows > 0) {
                    $(gridId).data("kendoGrid").dataSource.read();
                }
            } else {
                const errorMessage = result.errorMessageList && result.errorMessageList.length > 0
                    ? result.errorMessageList.join('<br/>')
                    : 'Có lỗi xảy ra khi import file';
                notification.show({
                    title: "Lỗi!",
                    message: errorMessage
                }, "error");
            }

            // Close import dialog
            $(".k-dialog").each(function () {
                const dialog = $(this).data("kendoDialog");
                if (dialog) {
                    dialog.close();
                }
            });

        } catch (error) {
            $("#importProgress").hide();
            console.error('Error importing file:', error);
            notification.show({
                title: "Lỗi!",
                message: "Lỗi khi import file: " + error.message
            }, "error");
        }
    }

    // Show import result function
    function showImportResult(importResult) {
        let resultHtml = `
            <div class="import-result">
                <h4>Kết quả Import</h4>
                <div class="result-summary">
                    <p><strong>Tổng số dòng xử lý:</strong> ${importResult.totalRows}</p>
                    <p><strong>Thành công:</strong> <span class="text-success">${importResult.successfulRows}</span></p>
                    <p><strong>Thất bại:</strong> <span class="text-danger">${importResult.failedRows}</span></p>
                </div>
        `;

        if (importResult.errors && importResult.errors.length > 0) {
            resultHtml += `
                <div class="error-details">
                    <h5>Chi tiết lỗi:</h5>
                    <div class="error-list" style="max-height: 300px; overflow-y: auto;">
            `;

            importResult.errors.forEach(error => {
                resultHtml += `
                    <div class="error-item" style="margin-bottom: 10px; padding: 10px; border-left: 3px solid #dc3545; background-color: #f8f9fa;">
                        <strong>Dòng ${error.rowNumber}:</strong> ${error.productName || 'N/A'}<br/>
                        <span class="text-danger">${error.errorMessages.join(', ')}</span>
                    </div>
                `;
            });

            resultHtml += `
                    </div>
                </div>
            `;
        }

        resultHtml += `</div>`;

        const resultDialog = $(resultHtml).kendoDialog({
            width: "600px",
            title: "Kết quả Import Excel",
            closable: true,
            modal: true,
            actions: [
                {
                    text: "Đóng",
                    primary: true,
                    action: function (e) {
                        return true; // Close dialog
                    }
                }
            ]
        }).data("kendoDialog");

        resultDialog.open();

        // Show notification
        if (importResult.failedRows === 0) {
            notification.show({
                title: "Thành công!",
                message: `Import thành công ${importResult.successfulRows} sản phẩm!`
            }, "success");
        } else {
            notification.show({
                title: "Hoàn tất!",
                message: `Import hoàn tất với ${importResult.successfulRows} thành công và ${importResult.failedRows} lỗi`
            }, "info");
        }
    }


        // ===== PRICE CALCULATOR FUNCTIONS =====
    function addPriceCalculatorButton() {
        // Find the defaultPrice field container
        var defaultPriceField = $("#formCreateAndEdit").find("[name='defaultPrice']");
        if (defaultPriceField.length > 0) {
            var fieldContainer = defaultPriceField.closest(".k-form-field");
            var numericWrapper = fieldContainer.find(".k-numerictextbox");

            // Check if button already exists
            if (fieldContainer.find(".price-calculator-btn").length === 0) {
                // Create container for input and button
                var containerDiv = $('<div class="default-price-container"></div>');

                // Move the numeric textbox into the container
                numericWrapper.wrap(containerDiv);

                // Create the calculator button
                var calculatorBtn = $(`
                    <button type="button" class="price-calculator-btn" title="Tính toán giá bán" id="btn_calculator">
                        Tính giá
                    </button>
                `);
                // Add click event to the button
                calculatorBtn.on('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    openPriceCalculator(formData);
                });

                // Insert the button after the numeric textbox
                numericWrapper.after(calculatorBtn);


                $("#btn_calculator").kendoButton({
                    icon: "calculator",
                    themeColor: "primary",
                    fillMode: "solid",
                    size: "medium"
                });
            }
        }
    }

    function openPriceCalculator() {
        var currentPrice = formData.defaultPrice || 0;

        // Create a div for the price calculator window
        var priceCalculatorDiv = $("<div id='priceCalculatorWindow'><form id='priceCalculatorForm'></form></div>");

        // Form data for price calculator
        var priceFormData = {
            costPrice: currentPrice,
            profitMargin: 0,
            finalPrice: currentPrice
        };

        // Initialize Kendo Form
        $("#priceCalculatorForm", priceCalculatorDiv).kendoForm({
            layout: "grid",
            grid: {
                cols: 1,
                gutter: "1rem"
            },
            formData: priceFormData,
            items: [
                {
                    field: "costPrice",
                    title: "Giá tiền",
                    label: "Giá tiền (VND) (*):",
                    colSpan: 1,
                    editor: "NumericTextBox",
                    editorOptions: {
                        format: "n0",
                        decimals: 0,
                        min: 0,
                        step: 1000,
                        placeholder: "Nhập giá tiền...",
                        change: function(e) {
                            priceFormData.costPrice = this.value() || 0;
                            calculatePriceInForm();
                        }
                    },
                    validation: {
                        validationMessage: "Vui lòng nhập giá tiền hợp lệ",
                        required: true
                    }
                },
                {
                    field: "profitMargin",
                    title: "Phần trăm lợi nhuận",
                    label: "Phần trăm lợi nhuận (%) (*):",
                    colSpan: 1,
                    editor: "NumericTextBox",
                    editorOptions: {
                        format: "n2",
                        decimals: 2,
                        min: 0,
                        max: 1000,
                        step: 0.1,
                        placeholder: "Nhập % lợi nhuận...",
                        change: function(e) {
                            priceFormData.profitMargin = this.value() || 0;
                            calculatePriceInForm();
                        }
                    },
                    validation: {
                        validationMessage: "Vui lòng nhập phần trăm lợi nhuận hợp lệ",
                        required: true
                    }
                },
                {
                    field: "finalPrice",
                    title: "Thành tiền",
                    label: "Thành tiền (VND):",
                    colSpan: 1,
                    editor: "NumericTextBox",
                    editorOptions: {
                        format: "n0",
                        decimals: 0,
                        enable: false
                    },
                    attributes: {
                        style: "background-color: #e8f5e8; font-weight: bold;"
                    }
                }
            ],
            messages: {
                submit: "Xác nhận",
                clear: "Hủy"
            },
            submit: function (e) {
                e.preventDefault();
                confirmPriceCalculationFromForm();
            },
            clear: function (e) {
                e.preventDefault();
                closePriceCalculator();
            }
        });

        // Initialize Kendo Window
        var priceCalculatorWindow = priceCalculatorDiv.kendoWindow({
            width: "450px",
            height: "auto",
            title: "Tính toán giá bán",
            visible: false,
            actions: ["Close"],
            resizable: false,
            draggable: true,
            modal: true,
            close: function (e) {
                $(this.element).remove();
            }
        }).data("kendoWindow");

        priceCalculatorWindow.element.addClass("price-calculator-popup");
        priceCalculatorWindow.center().open();

        // Calculate initial price if current price exists
        if (currentPrice > 0) {
            calculatePriceInForm();
        }

        // Ensure finalPrice field is disabled and styled correctly
        setTimeout(() => {
            var form = $("#priceCalculatorForm").data("kendoForm");
            if (form) {
                // Disable finalPrice field
                var finalPriceField = form.element.find("[name='finalPrice']");
                var finalPriceNumeric = finalPriceField.data("kendoNumericTextBox");
                if (finalPriceNumeric) {
                    finalPriceNumeric.enable(false);
                    finalPriceField.attr('readonly', true);
                    finalPriceField.attr('tabindex', '-1');
                }

                // Focus on first input
                var costPriceField = form.element.find("[name='costPrice']");
                if (costPriceField.length > 0) {
                    var numericTextBox = costPriceField.data("kendoNumericTextBox");
                    if (numericTextBox) {
                        numericTextBox.focus();
                    }
                }
            }
        }, 300);

        // Helper function to calculate price within the form
        function calculatePriceInForm() {
            var costPrice = priceFormData.costPrice || 0;
            var profitMargin = priceFormData.profitMargin || 0;

            if (costPrice > 0 && profitMargin >= 0) {
                var finalPrice = costPrice + (costPrice * profitMargin / 100);
                priceFormData.finalPrice = finalPrice;

                // Update the final price field
                var form = $("#priceCalculatorForm").data("kendoForm");
                if (form) {
                    var finalPriceField = form.element.find("[name='finalPrice']");
                    var finalPriceNumeric = finalPriceField.data("kendoNumericTextBox");
                    if (finalPriceNumeric) {
                        // Temporarily enable to set value, then disable again
                        finalPriceNumeric.enable(true);
                        finalPriceNumeric.value(finalPrice);
                        finalPriceNumeric.enable(false);
                    }
                }
            } else {
                // Reset final price if inputs are invalid
                priceFormData.finalPrice = 0;
                var form = $("#priceCalculatorForm").data("kendoForm");
                if (form) {
                    var finalPriceField = form.element.find("[name='finalPrice']");
                    var finalPriceNumeric = finalPriceField.data("kendoNumericTextBox");
                    if (finalPriceNumeric) {
                        finalPriceNumeric.enable(true);
                        finalPriceNumeric.value(0);
                        finalPriceNumeric.enable(false);
                    }
                }
            }
        }

        // Helper function to confirm price calculation from form
        function confirmPriceCalculationFromForm() {
            var form = $("#priceCalculatorForm").data("kendoForm");
            if (!form.validate()) {
                kendo.alert("Vui lòng điền đầy đủ thông tin bắt buộc!");
                return;
            }

            var costPrice = priceFormData.costPrice || 0;
            var profitMargin = priceFormData.profitMargin || 0;

            if (costPrice <= 0) {
                kendo.alert("Vui lòng nhập giá tiền hợp lệ!");
                return;
            }

            if (profitMargin < 0) {
                kendo.alert("Vui lòng nhập phần trăm lợi nhuận hợp lệ!");
                return;
            }

            var finalPrice = costPrice + (costPrice * profitMargin / 100);

            // Update the default price field in the main form
            var mainForm = $("#formCreateAndEdit").data("kendoForm");
            if (mainForm) {
                var numericTextBox = mainForm.element.find("[name='defaultPrice']").data("kendoNumericTextBox");
                if (numericTextBox) {
                    numericTextBox.value(finalPrice);
                    formData.defaultPrice = finalPrice;
                }
            }

            // Close the popup
            closePriceCalculator();

            // Show success notification
            notification.show({
                title: "Thành công!",
                message: "Đã cập nhật giá mặc định thành công!"
            }, "success");
        }
    }



    function closePriceCalculator() {
        var windows = $("#priceCalculatorWindow").data("kendoWindow");
        if (windows) {
            windows.close();
        }
    }

    function formatCurrency(amount) {
        return new Intl.NumberFormat('vi-VN', {
            style: 'currency',
            currency: 'VND',
            minimumFractionDigits: 0,
            maximumFractionDigits: 0
        }).format(amount);
    }

</script>
<script type="text/javascript">
    InitGrid();
    InitKendoToolBar();
    $(document).ready(function () {
        $(window).trigger("resize");

    });
</script>
<style>
    .k-form-buttons {
        justify-content: flex-end;
    }

    /* Responsive grid layout for product form */
    @@media (max-width: 768px) {
        #formCreateAndEdit .k-form-layout {
            grid-template-columns: 1fr !important;
        }

        #formCreateAndEdit .k-form-field {
            grid-column: 1 !important;
        }
        /* Adjust window width for mobile */
        .k-window {
            width: 95% !important;
            max-width: 500px !important;
        }
    }

    /* Ensure proper spacing in grid layout */
    #formCreateAndEdit .k-form-layout {
        gap: 1rem;
    }

    #formCreateAndEdit .k-form-field {
        margin-bottom: 0;
    }

    /* Import dialog styles */
    .import-result {
        padding: 15px;
    }

    .result-summary {
        background-color: #f8f9fa;
        padding: 15px;
        border-radius: 5px;
        margin-bottom: 15px;
    }

        .result-summary p {
            margin: 5px 0;
        }

    .error-details {
        margin-top: 15px;
    }

    .error-list {
        border: 1px solid #dee2e6;
        border-radius: 5px;
        padding: 10px;
    }

    .error-item {
        border-left: 3px solid #dc3545 !important;
        background-color: #f8f9fa !important;
        margin-bottom: 10px !important;
        padding: 10px !important;
        border-radius: 3px;
    }

    .text-success {
        color: #28a745 !important;
    }

    .text-danger {
        color: #dc3545 !important;
    }

    .text-warning {
        color: #ffc107 !important;
    }

    /* Import progress styles */
    #importProgress {
        text-align: center;
        padding: 20px;
    }

    .k-progressbar {
        width: 100%;
        height: 20px;
        background-color: #e9ecef;
        border-radius: 10px;
        overflow: hidden;
    }

    .k-progress-status-wrap {
        padding: 10px 0;
    }

    .k-progress-status {
        font-weight: 500;
        color: #495057;
    }

    .default-price-container{
        display: flex;
        align-items: center;
        gap: 10px;

    }
    .price-calculator-btn{
        width: 180px;
    }
</style>
