/* Vendor Page Mobile Responsive - Simple Overflow Solution */

/* Enable horizontal scroll for grid on mobile */
@media (max-width: 768px) {
    /* Grid container */
    #divContent {
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
    }

    /* Kendo Grid */
    #gridId.k-grid {
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
    }

    /* Grid header */
    #gridId .k-grid-header {
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
    }

    /* Grid content */
    #gridId .k-grid-content {
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
    }

    /* Prevent text wrapping */
    #gridId .k-grid-content td,
    #gridId .k-grid-header .k-header {
        white-space: nowrap;
    }

    /* Improve text readability */
    #gridId .k-grid-content td {
        font-size: 13px;
        padding: 8px 6px;
    }

    #gridId .k-grid-header .k-header {
        font-size: 12px;
        padding: 10px 6px;
    }
}

/* For smaller screens */
@media (max-width: 576px) {
    #gridId .k-grid-content td {
        font-size: 12px;
        padding: 6px 4px;
    }

    #gridId .k-grid-header .k-header {
        font-size: 11px;
        padding: 8px 4px;
    }
}

/* For very small screens */
@media (max-width: 480px) {
    #gridId .k-grid-content td {
        font-size: 11px;
        padding: 6px 3px;
    }

    #gridId .k-grid-header .k-header {
        font-size: 10px;
        padding: 8px 3px;
    }
}


/* Simple grid overflow for mobile */
@media (max-width: 768px) {
    #divContent {
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
    }

    #gridId.k-grid {
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
    }

    #gridId .k-grid-header {
        overflow-x: auto;
    }

    #gridId .k-grid-content {
        overflow-x: auto;
    }
}