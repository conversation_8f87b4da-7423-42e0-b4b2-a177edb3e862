/* Vendor Page Mobile Responsive Styles */

/* General mobile improvements */
@media (max-width: 768px) {
    /* Page title */
    .demo-section.wide.title {
        font-size: 18px;
        margin-bottom: 15px;
        text-align: center;
    }

    /* Grid container */
    #divContent {
        padding: 0 5px;
        overflow-x: auto;
    }

    /* Vendor grid specific styles */
    #gridId.k-grid {
        font-size: 13px;
        border: 1px solid #ddd;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    /* Grid header improvements */
    #gridId .k-grid-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        font-weight: 600;
    }

    #gridId .k-grid-header .k-header {
        color: white;
        font-size: 12px;
        padding: 12px 8px;
        text-align: center;
        border-right: 1px solid rgba(255, 255, 255, 0.2);
        min-width: 120px;
    }

    /* Grid content improvements */
    #gridId .k-grid-content {
        max-height: 70vh;
        overflow-y: auto;
    }

    #gridId .k-grid-content td {
        padding: 10px 8px;
        font-size: 12px;
        border-right: 1px solid #eee;
        vertical-align: middle;
        text-align: center;
        min-width: 120px;
    }

    /* Alternating row colors */
    #gridId .k-grid-content tr:nth-child(even) {
        background-color: #f8f9fa;
    }

    #gridId .k-grid-content tr:hover {
        background-color: #e3f2fd;
    }

    /* Column specific widths for mobile */
    #gridId .k-grid-content td:nth-child(1), /* STT */
    #gridId .k-grid-header .k-header:nth-child(1) {
        min-width: 60px;
        max-width: 80px;
    }

    #gridId .k-grid-content td:nth-child(2), /* Mã nhà cung cấp */
    #gridId .k-grid-header .k-header:nth-child(2) {
        min-width: 120px;
        max-width: 150px;
    }

    #gridId .k-grid-content td:nth-child(3), /* Họ tên */
    #gridId .k-grid-header .k-header:nth-child(3) {
        min-width: 150px;
        max-width: 200px;
    }

    #gridId .k-grid-content td:nth-child(4), /* Địa chỉ */
    #gridId .k-grid-header .k-header:nth-child(4) {
        min-width: 180px;
        max-width: 250px;
    }

    #gridId .k-grid-content td:nth-child(5), /* Email */
    #gridId .k-grid-header .k-header:nth-child(5) {
        min-width: 160px;
        max-width: 200px;
    }

    #gridId .k-grid-content td:nth-child(6), /* Mã số thuế */
    #gridId .k-grid-header .k-header:nth-child(6) {
        min-width: 120px;
        max-width: 150px;
    }

    #gridId .k-grid-content td:nth-child(7), /* Số điện thoại */
    #gridId .k-grid-header .k-header:nth-child(7) {
        min-width: 130px;
        max-width: 160px;
    }

    #gridId .k-grid-content td:nth-child(8), /* Ngày cập nhật */
    #gridId .k-grid-header .k-header:nth-child(8) {
        min-width: 140px;
        max-width: 180px;
    }

    #gridId .k-grid-content td:nth-child(9), /* Người cập nhật */
    #gridId .k-grid-header .k-header:nth-child(9) {
        min-width: 120px;
        max-width: 150px;
    }

    #gridId .k-grid-content td:nth-child(10), /* Thao tác */
    #gridId .k-grid-header .k-header:nth-child(10) {
        min-width: 140px;
        max-width: 180px;
    }

    /* Action buttons improvements */
    .action-buttons {
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 6px;
        flex-wrap: nowrap;
    }

    .btn-action {
        width: 34px;
        height: 34px;
        border-radius: 6px;
        border: none;
        cursor: pointer;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 12px;
    }

    .btn-action:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    }

    /* Toolbar improvements */
    .k-toolbar {
        padding: 10px;
        background: #f8f9fa;
        border-radius: 8px 8px 0 0;
        border-bottom: 1px solid #ddd;
    }

    .k-toolbar .k-button {
        margin: 2px;
        padding: 8px 12px;
        font-size: 12px;
        border-radius: 6px;
    }

    /* Pager improvements */
    .k-pager-wrap {
        padding: 10px;
        background: #f8f9fa;
        border-radius: 0 0 8px 8px;
    }

    .k-pager-wrap .k-pager-numbers .k-link {
        padding: 6px 10px;
        margin: 2px;
        border-radius: 4px;
    }
}

/* Small mobile screens */
@media (max-width: 576px) {
    .demo-section.wide.title {
        font-size: 16px;
        margin-bottom: 10px;
    }

    #divContent {
        padding: 0 2px;
    }

    #gridId .k-grid-header .k-header {
        font-size: 11px;
        padding: 10px 6px;
        min-width: 100px;
    }

    #gridId .k-grid-content td {
        font-size: 11px;
        padding: 8px 6px;
        min-width: 100px;
    }

    .btn-action {
        width: 30px;
        height: 30px;
        font-size: 11px;
    }

    .action-buttons {
        gap: 4px;
    }

    /* Reduce column widths for very small screens */
    #gridId .k-grid-content td:nth-child(1),
    #gridId .k-grid-header .k-header:nth-child(1) {
        min-width: 50px;
        max-width: 60px;
    }

    #gridId .k-grid-content td:nth-child(2),
    #gridId .k-grid-header .k-header:nth-child(2) {
        min-width: 100px;
        max-width: 120px;
    }

    #gridId .k-grid-content td:nth-child(10),
    #gridId .k-grid-header .k-header:nth-child(10) {
        min-width: 120px;
        max-width: 140px;
    }
}

/* Extra small screens */
@media (max-width: 480px) {
    #gridId .k-grid-header .k-header {
        font-size: 10px;
        padding: 8px 4px;
        min-width: 80px;
    }

    #gridId .k-grid-content td {
        font-size: 10px;
        padding: 6px 4px;
        min-width: 80px;
    }

    .btn-action {
        width: 28px;
        height: 28px;
        font-size: 10px;
    }

    .action-buttons {
        gap: 2px;
        flex-wrap: wrap;
    }
}

/* Text overflow handling for long content */
@media (max-width: 768px) {
    #gridId .k-grid-content td {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        position: relative;
    }

    /* Tooltip on hover for truncated text */
    #gridId .k-grid-content td:hover {
        overflow: visible;
        white-space: normal;
        word-wrap: break-word;
        z-index: 10;
        background: white;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
        border: 1px solid #ddd;
        border-radius: 4px;
        padding: 8px;
    }
}
