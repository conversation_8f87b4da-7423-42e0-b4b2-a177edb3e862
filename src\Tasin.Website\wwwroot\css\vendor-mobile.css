/* Vendor Page Mobile Responsive - Force Horizontal Scroll */

/* Enable horizontal scroll for grid on mobile */
@media (max-width: 768px) {
    /* Grid container */
    #divContent {
        overflow-x: auto !important;
        -webkit-overflow-scrolling: touch;
        width: 100%;
    }

    /* Kendo Grid - Force minimum width */
    #gridId.k-grid {
        overflow-x: auto !important;
        -webkit-overflow-scrolling: touch;
        min-width: 1200px !important;
        width: auto !important;
    }

    /* Grid header */
    #gridId .k-grid-header {
        overflow-x: auto !important;
        -webkit-overflow-scrolling: touch;
        min-width: 1200px !important;
    }

    /* Grid content */
    #gridId .k-grid-content {
        overflow-x: auto !important;
        -webkit-overflow-scrolling: touch;
        min-width: 1200px !important;
    }

    /* Force table elements to maintain width */
    #gridId .k-grid-header table,
    #gridId .k-grid-content table {
        min-width: 1200px !important;
        width: auto !important;
        table-layout: auto !important;
    }

    /* Prevent text wrapping and ensure minimum column width */
    #gridId .k-grid-content td,
    #gridId .k-grid-header .k-header {
        white-space: nowrap !important;
        min-width: 100px !important;
    }

    /* Make scrollbars visible */
    #divContent::-webkit-scrollbar,
    #gridId::-webkit-scrollbar,
    #gridId .k-grid-header::-webkit-scrollbar,
    #gridId .k-grid-content::-webkit-scrollbar {
        height: 12px !important;
        width: 12px !important;
    }

    #divContent::-webkit-scrollbar-track,
    #gridId::-webkit-scrollbar-track,
    #gridId .k-grid-header::-webkit-scrollbar-track,
    #gridId .k-grid-content::-webkit-scrollbar-track {
        background: #f1f1f1 !important;
        border-radius: 6px !important;
    }

    #divContent::-webkit-scrollbar-thumb,
    #gridId::-webkit-scrollbar-thumb,
    #gridId .k-grid-header::-webkit-scrollbar-thumb,
    #gridId .k-grid-content::-webkit-scrollbar-thumb {
        background: #888 !important;
        border-radius: 6px !important;
    }

    #divContent::-webkit-scrollbar-thumb:hover,
    #gridId::-webkit-scrollbar-thumb:hover,
    #gridId .k-grid-header::-webkit-scrollbar-thumb:hover,
    #gridId .k-grid-content::-webkit-scrollbar-thumb:hover {
        background: #555 !important;
    }
}

    /* Improve text readability */
    #gridId .k-grid-content td {
        font-size: 13px;
        padding: 8px 6px;
    }

    #gridId .k-grid-header .k-header {
        font-size: 12px;
        padding: 10px 6px;
    }
}

/* For smaller screens */
@media (max-width: 576px) {
    #gridId .k-grid-content td {
        font-size: 12px;
        padding: 6px 4px;
    }

    #gridId .k-grid-header .k-header {
        font-size: 11px;
        padding: 8px 4px;
    }
}

/* For very small screens */
@media (max-width: 480px) {
    #gridId .k-grid-content td {
        font-size: 11px;
        padding: 6px 3px;
    }

    #gridId .k-grid-header .k-header {
        font-size: 10px;
        padding: 8px 3px;
    }
}


/* Simple grid overflow for mobile */
@media (max-width: 768px) {
    #divContent {
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
    }

    #gridId.k-grid {
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
    }

    #gridId .k-grid-header {
        overflow-x: auto;
    }

    #gridId .k-grid-content {
        overflow-x: auto;
    }
}