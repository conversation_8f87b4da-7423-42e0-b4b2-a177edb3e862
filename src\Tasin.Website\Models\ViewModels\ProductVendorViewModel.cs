using System.ComponentModel.DataAnnotations;

namespace Tasin.Website.Models.ViewModels
{
    /// <summary>
    /// View model for product-vendor relationship
    /// </summary>
    public class ProductVendorViewModel : BaseViewModel
    {
        /// <summary>
        /// Vendor ID
        /// </summary>
        [Required]
        [Display(Name = "Vendor ID")]
        public int Vendor_ID { get; set; }

        /// <summary>
        /// Product ID
        /// </summary>
        [Required]
        [Display(Name = "Product ID")]
        public int Product_ID { get; set; }

        /// <summary>
        /// Price
        /// </summary>
        [Display(Name = "Price")]
        public decimal? Price { get; set; }

        /// <summary>
        /// Unit Price
        /// </summary>
        [Display(Name = "Unit Price")]
        public decimal? UnitPrice { get; set; }

        /// <summary>
        /// Priority
        /// </summary>
        [Display(Name = "Priority")]
        public int? Priority { get; set; }

        /// <summary>
        /// Description
        /// </summary>
        [Display(Name = "Description")]
        public string? Description { get; set; }

        // Navigation properties for display
        /// <summary>
        /// Vendor name for display
        /// </summary>
        [Display(Name = "Vendor Name")]
        public string? VendorName { get; set; }

        /// <summary>
        /// Product name for display
        /// </summary>
        [Display(Name = "Product Name")]
        public string? ProductName { get; set; }

        /// <summary>
        /// Product code for display
        /// </summary>
        [Display(Name = "Product Code")]
        public string? ProductCode { get; set; }
    }

    /// <summary>
    /// View model for bulk adding products to vendor
    /// </summary>
    public class BulkProductVendorViewModel
    {
        /// <summary>
        /// Vendor ID
        /// </summary>
        [Required]
        public int VendorId { get; set; }

        /// <summary>
        /// List of products to add
        /// </summary>
        [Required]
        public List<ProductVendorItemViewModel> Products { get; set; } = new();
    }

    /// <summary>
    /// Individual product item for bulk operations
    /// </summary>
    public class ProductVendorItemViewModel
    {
        /// <summary>
        /// Product ID
        /// </summary>
        [Required]
        public int Product_ID { get; set; }

        /// <summary>
        /// Unit Price
        /// </summary>
        public decimal? UnitPrice { get; set; }

        /// <summary>
        /// Priority
        /// </summary>
        public int? Priority { get; set; }

        /// <summary>
        /// Description
        /// </summary>
        public string? Description { get; set; }

        // Display properties
        public string? ProductName { get; set; }
        public string? ProductCode { get; set; }
    }

    /// <summary>
    /// Model for Excel import of product-vendor relationships
    /// </summary>
    public class ProductVendorExcelImportModel
    {
        /// <summary>
        /// Row number in Excel (for error reporting)
        /// </summary>
        public int RowNumber { get; set; }

        /// <summary>
        /// Vendor code (required)
        /// </summary>
        [Required(ErrorMessage = "Mã nhà cung cấp không được để trống")]
        public string VendorCode { get; set; } = string.Empty;

        /// <summary>
        /// Product code (required)
        /// </summary>
        [Required(ErrorMessage = "Mã sản phẩm không được để trống")]
        public string ProductCode { get; set; } = string.Empty;

        /// <summary>
        /// Unit price
        /// </summary>
        public decimal? UnitPrice { get; set; }

        /// <summary>
        /// Priority (lower number = higher priority)
        /// </summary>
        public int? Priority { get; set; }

        /// <summary>
        /// Description
        /// </summary>
        public string? Description { get; set; }
    }

    /// <summary>
    /// Result model for ProductVendor Excel import operation
    /// </summary>
    public class ProductVendorExcelImportResult
    {
        /// <summary>
        /// Total rows processed
        /// </summary>
        public int TotalRows { get; set; }

        /// <summary>
        /// Successfully imported rows
        /// </summary>
        public int SuccessfulRows { get; set; }

        /// <summary>
        /// Failed rows
        /// </summary>
        public int FailedRows { get; set; }

        /// <summary>
        /// List of errors by row
        /// </summary>
        public List<ProductVendorExcelImportError> Errors { get; set; } = new List<ProductVendorExcelImportError>();

        /// <summary>
        /// Overall success status
        /// </summary>
        public bool IsSuccess => FailedRows == 0;

        /// <summary>
        /// Summary message
        /// </summary>
        public string Message => $"Đã xử lý {TotalRows} dòng. Thành công: {SuccessfulRows}, Thất bại: {FailedRows}";
    }

    /// <summary>
    /// Error information for a specific ProductVendor import row
    /// </summary>
    public class ProductVendorExcelImportError
    {
        /// <summary>
        /// Row number
        /// </summary>
        public int RowNumber { get; set; }

        /// <summary>
        /// Vendor code from the row
        /// </summary>
        public string VendorCode { get; set; } = string.Empty;

        /// <summary>
        /// Product code from the row
        /// </summary>
        public string ProductCode { get; set; } = string.Empty;

        /// <summary>
        /// List of error messages
        /// </summary>
        public List<string> ErrorMessages { get; set; } = new List<string>();
    }
}
