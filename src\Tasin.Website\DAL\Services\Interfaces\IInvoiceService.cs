using Tasin.Website.Common.CommonModels.BaseModels;
using Tasin.Website.Models.ViewModels;

namespace Tasin.Website.DAL.Services.Interfaces
{
    /// <summary>
    /// Interface for invoice service operations
    /// </summary>
    public interface IInvoiceService
    {
        /// <summary>
        /// Generate invoice from purchase order
        /// </summary>
        /// <param name="purchaseOrderId">Purchase order ID</param>
        /// <returns>Invoice view model</returns>
        Task<Acknowledgement<InvoiceViewModel>> GenerateInvoiceFromPurchaseOrder(int purchaseOrderId);

        /// <summary>
        /// Export invoice as PDF
        /// </summary>
        /// <param name="invoiceData">Invoice data</param>
        /// <returns>PDF file as byte array</returns>
        Task<byte[]> ExportInvoiceAsPdf(InvoiceViewModel invoiceData);

        /// <summary>
        /// Export invoice as Excel
        /// </summary>
        /// <param name="invoiceData">Invoice data</param>
        /// <returns>Excel file as byte array</returns>
        Task<byte[]> ExportInvoiceAsExcel(InvoiceViewModel invoiceData);



        /// <summary>
        /// Generate invoice HTML template
        /// </summary>
        /// <param name="invoiceData">Invoice data</param>
        /// <returns>HTML string</returns>
        Task<string> GenerateInvoiceHtml(InvoiceViewModel invoiceData);

        /// <summary>
        /// Generate delivery note from purchase order
        /// </summary>
        /// <param name="purchaseOrderId">Purchase order ID</param>
        /// <returns>Delivery note view model</returns>
        Task<Acknowledgement<DeliveryNoteViewModel>> GenerateDeliveryNoteFromPurchaseOrder(int purchaseOrderId);

        /// <summary>
        /// Export delivery note as PDF
        /// </summary>
        /// <param name="deliveryNoteData">Delivery note data</param>
        /// <returns>PDF file as byte array</returns>
        Task<byte[]> ExportDeliveryNoteAsPdf(DeliveryNoteViewModel deliveryNoteData);

        /// <summary>
        /// Generate delivery note HTML template
        /// </summary>
        /// <param name="deliveryNoteData">Delivery note data</param>
        /// <returns>HTML string</returns>
        Task<string> GenerateDeliveryNoteHtml(DeliveryNoteViewModel deliveryNoteData);
    }
}
