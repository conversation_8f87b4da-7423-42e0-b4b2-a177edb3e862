using AutoMapper;
using LinqKit;
using Microsoft.EntityFrameworkCore;
using Tasin.Website.Common.CommonModels;
using Tasin.Website.Common.CommonModels.BaseModels;
using Tasin.Website.Common.Helper;
using Tasin.Website.Common.Services;
using Tasin.Website.DAL.Interfaces;
using Tasin.Website.DAL.Repository;
using Tasin.Website.DAL.Services.WebInterfaces;
using Tasin.Website.Domains.DBContexts;
using Tasin.Website.Domains.Entitites;
using Tasin.Website.Models.SearchModels;
using Tasin.Website.Models.ViewModels;
using ClosedXML.Excel;
using Tasin.Website.Common.Enums;
using System.ComponentModel.DataAnnotations;

namespace Tasin.Website.DAL.Services.WebServices
{
    public class ProductVendorService : BaseService<ProductVendorService>, IProductVendorService
    {
        private readonly IProduct_VendorRepository _productVendorRepository;
        private readonly IProductRepository _productRepository;
        private readonly IVendorRepository _vendorRepository;
        private readonly IMapper _mapper;

        public ProductVendorService(
            ILogger<ProductVendorService> logger,
            IUserRepository userRepository,
            IProduct_VendorRepository productVendorRepository,
            IProductRepository productRepository,
            IVendorRepository vendorRepository,
            IRoleRepository roleRepository,
            IHttpContextAccessor httpContextAccessor,
            IConfiguration configuration,
            ICurrentUserContext currentUserContext,
            SampleDBContext dbContext,
            IMapper mapper
            ) : base(logger, configuration, userRepository, roleRepository, httpContextAccessor, currentUserContext, dbContext)
        {
            _productVendorRepository = productVendorRepository;
            _productRepository = productRepository;
            _vendorRepository = vendorRepository;
            _mapper = mapper;
        }

        public async Task<Acknowledgement<JsonResultPaging<List<ProductVendorViewModel>>>> GetProductVendorList(ProductVendorSearchModel searchModel)
        {
            var response = new Acknowledgement<JsonResultPaging<List<ProductVendorViewModel>>>();
            try
            {
                var predicate = PredicateBuilder.New<Product_Vendor>(true);

                if (searchModel.VendorId.HasValue)
                {
                    predicate = predicate.And(pv => pv.Vendor_ID == searchModel.VendorId.Value);
                }

                if (searchModel.ProductId.HasValue)
                {
                    predicate = predicate.And(pv => pv.Product_ID == searchModel.ProductId.Value);
                }

                if (searchModel.MinPrice.HasValue)
                {
                    predicate = predicate.And(pv => pv.Price >= searchModel.MinPrice.Value);
                }

                if (searchModel.MaxPrice.HasValue)
                {
                    predicate = predicate.And(pv => pv.Price <= searchModel.MaxPrice.Value);
                }

                if (searchModel.Priority.HasValue)
                {
                    predicate = predicate.And(pv => pv.Priority == searchModel.Priority.Value);
                }

                var productVendorQuery = await _productVendorRepository.ReadOnlyRespository.GetWithPagingAsync(
                new PagingParameters(searchModel.PageNumber, searchModel.PageSize),
                predicate,
                q => q.OrderBy(pv => pv.Vendor_ID).ThenBy(pv => pv.Priority ?? int.MaxValue),
                "Vendor,Product"
                );

                var productVendorViewModels = _mapper.Map<List<ProductVendorViewModel>>(productVendorQuery.Data);

                // Set display names
                foreach (var item in productVendorViewModels)
                {
                    var productVendor = productVendorQuery.Data.FirstOrDefault(pv => 
                        pv.Vendor_ID == item.Vendor_ID && pv.Product_ID == item.Product_ID);
                    
                    if (productVendor != null)
                    {
                        item.VendorName = productVendor.Vendor?.Name;
                        item.ProductName = productVendor.Product?.Name;
                        item.ProductCode = productVendor.Product?.Code;
                    }
                }

                response.Data = new JsonResultPaging<List<ProductVendorViewModel>>
                {
                    Data = productVendorViewModels,
                    PageNumber = searchModel.PageNumber,
                    PageSize = searchModel.PageSize,
                    Total = productVendorQuery.TotalRecords
                };
                response.IsSuccess = true;
                return response;
            }
            catch (Exception ex)
            {
                response.ExtractMessage(ex);
                _logger.LogError($"GetProductVendorList: {ex.Message}");
                return response;
            }
        }

        public async Task<Acknowledgement<List<ProductVendorViewModel>>> GetProductsByVendorId(int vendorId)
        {
            var response = new Acknowledgement<List<ProductVendorViewModel>>();
            try
            {
                var productVendors = await _productVendorRepository.ReadOnlyRespository.GetAsync(
                    pv => pv.Vendor_ID == vendorId,
                    q => q.OrderBy(pv => pv.Priority ?? int.MaxValue),
                    null,
                    "Product", 
                    e => new ProductVendorViewModel
                    {
                        Vendor_ID = e.Vendor_ID,
                        Product_ID = e.Product_ID,
                        UnitPrice = e.UnitPrice,
                        Priority = e.Priority,
                        Description = e.Description,
                        VendorName = e.Vendor.Name,
                        ProductName = e.Product.Name,
                        ProductCode = e.Product.Code,

                    }
                );

                response.Data = productVendors;
                response.IsSuccess = true;
                return response;
            }
            catch (Exception ex)
            {
                response.ExtractMessage(ex);
                _logger.LogError($"GetProductsByVendorId: {ex.Message}");
                return response;
            }
        }

        public async Task<Acknowledgement<List<ProductVendorViewModel>>> GetVendorsByProductId(int productId)
        {
            var response = new Acknowledgement<List<ProductVendorViewModel>>();
            try
            {
                var productVendors = await _productVendorRepository.ReadOnlyRespository.GetAsync(
                    pv => pv.Product_ID == productId,
                    q => q.OrderBy(pv => pv.Priority ?? int.MaxValue),
                    null,
                    "Vendor"
                );

                var viewModels = _mapper.Map<List<ProductVendorViewModel>>(productVendors);
                
                // Set display names
                foreach (var item in viewModels)
                {
                    var productVendor = productVendors.FirstOrDefault(pv => 
                        pv.Vendor_ID == item.Vendor_ID && pv.Product_ID == item.Product_ID);
                    
                    if (productVendor?.Vendor != null)
                    {
                        item.VendorName = productVendor.Vendor.Name;
                    }
                }

                response.Data = viewModels;
                response.IsSuccess = true;
                return response;
            }
            catch (Exception ex)
            {
                response.ExtractMessage(ex);
                _logger.LogError($"GetVendorsByProductId: {ex.Message}");
                return response;
            }
        }

        public async Task<Acknowledgement<ProductVendorViewModel>> GetProductVendorById(int vendorId, int productId)
        {
            var response = new Acknowledgement<ProductVendorViewModel>();
            try
            {
                var productVendor = await _productVendorRepository.ReadOnlyRespository.FirstOrDefaultAsync(
                    pv => pv.Vendor_ID == vendorId && pv.Product_ID == productId,
                    "Vendor,Product"
                );

                if (productVendor == null)
                {
                    response.AddMessage("Không tìm thấy mối quan hệ sản phẩm-nhà cung cấp.");
                    return response;
                }

                var viewModel = _mapper.Map<ProductVendorViewModel>(productVendor);
                viewModel.VendorName = productVendor.Vendor?.Name;
                viewModel.ProductName = productVendor.Product?.Name;
                viewModel.ProductCode = productVendor.Product?.Code;

                response.Data = viewModel;
                response.IsSuccess = true;
                return response;
            }
            catch (Exception ex)
            {
                response.ExtractMessage(ex);
                _logger.LogError($"GetProductVendorById: {ex.Message}");
                return response;
            }
        }

        public async Task<Acknowledgement> CreateOrUpdateProductVendor(ProductVendorViewModel model)
        {
            var ack = new Acknowledgement();
            try
            {
                // Validate vendor exists
                var vendor = await _vendorRepository.Repository.FindAsync(model.Vendor_ID);
                if (vendor == null)
                {
                    ack.AddMessage("Không tìm thấy nhà cung cấp.");
                    return ack;
                }

                // Validate product exists
                var product = await _productRepository.Repository.FindAsync(model.Product_ID);
                if (product == null)
                {
                    ack.AddMessage("Không tìm thấy sản phẩm.");
                    return ack;
                }

                // Check if relationship already exists
                var existingProductVendor = await _productVendorRepository.Repository
                    .FirstOrDefaultAsync(pv => pv.Vendor_ID == model.Vendor_ID && pv.Product_ID == model.Product_ID);

                if (existingProductVendor != null)
                {
                    // Update existing relationship
                    existingProductVendor.Price = model.Price;
                    existingProductVendor.UnitPrice = model.UnitPrice;
                    existingProductVendor.Priority = model.Priority;
                    existingProductVendor.Description = model.Description;

                    await ack.TrySaveChangesAsync(res => res.UpdateAsync(existingProductVendor), _productVendorRepository.Repository);
                }
                else
                {
                    // Create new relationship
                    var newProductVendor = _mapper.Map<Product_Vendor>(model);
                    await ack.TrySaveChangesAsync(res => res.AddAsync(newProductVendor), _productVendorRepository.Repository);
                }

                return ack;
            }
            catch (Exception ex)
            {
                ack.ExtractMessage(ex);
                _logger.LogError($"CreateOrUpdateProductVendor: {ex.Message}");
                return ack;
            }
        }

        public async Task<Acknowledgement> BulkAddProductsToVendor(BulkProductVendorViewModel model)
        {
            var ack = new Acknowledgement();
            try
            {
                // Validate vendor exists
                var vendor = await _vendorRepository.Repository.FindAsync(model.VendorId);
                if (vendor == null)
                {
                    ack.AddMessage("Không tìm thấy nhà cung cấp.");
                    return ack;
                }

                if (model.Products == null || !model.Products.Any())
                {
                    ack.AddMessage("Danh sách sản phẩm không được để trống.");
                    return ack;
                }

                var productIds = model.Products.Select(p => p.Product_ID).ToList();
                var products = await _productRepository.ReadOnlyRespository.GetAsync(p => productIds.Contains(p.ID));

                if (products.Count != productIds.Count)
                {
                    ack.AddMessage("Một số sản phẩm không tồn tại trong hệ thống.");
                    return ack;
                }

                // Get existing relationships to avoid duplicates
                var existingRelationships = await _productVendorRepository.ReadOnlyRespository.GetAsync(
                    pv => pv.Vendor_ID == model.VendorId && productIds.Contains(pv.Product_ID));

                var existingProductIds = existingRelationships.Select(pv => pv.Product_ID).ToHashSet();

                var newProductVendors = new List<Product_Vendor>();
                var updateProductVendors = new List<Product_Vendor>();

                foreach (var productItem in model.Products)
                {
                    if (existingProductIds.Contains(productItem.Product_ID))
                    {
                        // Update existing relationship
                        var existing = existingRelationships.First(pv => pv.Product_ID == productItem.Product_ID);
                        existing.UnitPrice = productItem.UnitPrice;
                        existing.Priority = productItem.Priority;
                        existing.Description = productItem.Description;
                        updateProductVendors.Add(existing);
                    }
                    else
                    {
                        // Create new relationship
                        var newProductVendor = new Product_Vendor
                        {
                            Vendor_ID = model.VendorId,
                            Product_ID = productItem.Product_ID,
                            UnitPrice = productItem.UnitPrice,
                            Priority = productItem.Priority,
                            Description = productItem.Description
                        };
                        newProductVendors.Add(newProductVendor);
                    }
                }

                // Save changes
                if (newProductVendors.Any())
                {
                    await _productVendorRepository.Repository.AddRangeAsync(newProductVendors);
                }

                if (updateProductVendors.Any())
                {
                   await _productVendorRepository.Repository.UpdateRangeAsync(updateProductVendors);
                }

                await _productVendorRepository.Repository.SaveChangesAsync();
                ack.IsSuccess = true;
                return ack;
            }
            catch (Exception ex)
            {
                ack.ExtractMessage(ex);
                _logger.LogError($"BulkAddProductsToVendor: {ex.Message}");
                return ack;
            }
        }

        public async Task<Acknowledgement> DeleteProductVendor(int vendorId, int productId)
        {
            var ack = new Acknowledgement();
            try
            {
                var productVendor = await _productVendorRepository.Repository
                    .FirstOrDefaultAsync(pv => pv.Vendor_ID == vendorId && pv.Product_ID == productId);

                if (productVendor == null)
                {
                    ack.AddMessage("Không tìm thấy mối quan hệ sản phẩm-nhà cung cấp.");
                    return ack;
                }

                await ack.TrySaveChangesAsync(res => res.DeleteAsync(productVendor), _productVendorRepository.Repository);
                return ack;
            }
            catch (Exception ex)
            {
                ack.ExtractMessage(ex);
                _logger.LogError($"DeleteProductVendor: {ex.Message}");
                return ack;
            }
        }

        public async Task<Acknowledgement> BulkDeleteProductsFromVendor(int vendorId, List<int> productIds)
        {
            var ack = new Acknowledgement();
            try
            {
                if (productIds == null || !productIds.Any())
                {
                    ack.AddMessage("Danh sách sản phẩm không được để trống.");
                    return ack;
                }

                var productVendors = await _productVendorRepository.ReadOnlyRespository.GetAsync(
                    pv => pv.Vendor_ID == vendorId && productIds.Contains(pv.Product_ID));

                if (!productVendors.Any())
                {
                    ack.AddMessage("Không tìm thấy mối quan hệ sản phẩm-nhà cung cấp nào để xóa.");
                    return ack;
                }

                await _productVendorRepository.Repository.DeleteRangeAsync(productVendors);
                await _productVendorRepository.Repository.SaveChangesAsync();

                ack.IsSuccess = true;
                return ack;
            }
            catch (Exception ex)
            {
                ack.ExtractMessage(ex);
                _logger.LogError($"BulkDeleteProductsFromVendor: {ex.Message}");
                return ack;
            }
        }

        public async Task<Acknowledgement<ProductVendorExcelImportResult>> ImportProductVendorsFromExcel(IFormFile file)
        {
            var response = new Acknowledgement<ProductVendorExcelImportResult>();
            var result = new ProductVendorExcelImportResult();

            try
            {
                // Validate file
                if (file == null || file.Length == 0)
                {
                    response.AddMessage("Vui lòng chọn file Excel để import.");
                    return response;
                }

                var fileExtension = Path.GetExtension(file.FileName).ToLower();
                if (!FileHelper.ValidateFileExt(EFileType.Excel, fileExtension))
                {
                    response.AddMessage("File không đúng định dạng Excel (.xlsx, .xls, .xlsm, .csv).");
                    return response;
                }

                // Read Excel file
                var importModels = new List<ProductVendorExcelImportModel>();
                using (var stream = new MemoryStream())
                {
                    await file.CopyToAsync(stream);
                    stream.Position = 0;

                    using (var workbook = ExcelHelper.CreateWorkbook(stream))
                    {
                        var worksheet = workbook.Worksheet(1);
                        var rows = worksheet.RowsUsed().Skip(1); // Skip header row

                        int rowNumber = 2; // Start from row 2 (after header)
                        foreach (var row in rows)
                        {
                            var importModel = new ProductVendorExcelImportModel
                            {
                                RowNumber = rowNumber,
                                VendorCode = ExcelHelper.GetCellStringValue(row.Cell(1)),
                                ProductCode = ExcelHelper.GetCellStringValue(row.Cell(2)),
                                UnitPrice = ParseDecimal(ExcelHelper.GetCellStringValue(row.Cell(3))),
                                Priority = ParseInt(ExcelHelper.GetCellStringValue(row.Cell(4))),
                                Description = ExcelHelper.GetCellStringValue(row.Cell(5))
                            };

                            // Skip empty rows
                            if (string.IsNullOrWhiteSpace(importModel.VendorCode) &&
                                string.IsNullOrWhiteSpace(importModel.ProductCode))
                            {
                                rowNumber++;
                                continue;
                            }

                            importModels.Add(importModel);
                            rowNumber++;
                        }
                    }
                }

                result.TotalRows = importModels.Count;

                if (!importModels.Any())
                {
                    response.AddMessage("File Excel không có dữ liệu để import.");
                    return response;
                }

                // Validate and process each row
                var validImportModels = new List<ProductVendorExcelImportModel>();
                foreach (var importModel in importModels)
                {
                    var validationErrors = ValidateProductVendorImportModel(importModel);
                    if (validationErrors.Any())
                    {
                        result.FailedRows++;
                        result.Errors.Add(new ProductVendorExcelImportError
                        {
                            RowNumber = importModel.RowNumber,
                            VendorCode = importModel.VendorCode,
                            ProductCode = importModel.ProductCode,
                            ErrorMessages = validationErrors
                        });
                    }
                    else
                    {
                        validImportModels.Add(importModel);
                    }
                }

                // Get all vendor codes and product codes for batch lookup
                var vendorCodes = validImportModels.Select(m => m.VendorCode).Distinct().ToList();
                var productCodes = validImportModels.Select(m => m.ProductCode).Distinct().ToList();

                // Batch lookup vendors and products
                var vendors = await _vendorRepository.ReadOnlyRespository.GetAsync(
                    v => vendorCodes.Contains(v.Code));
                var products = await _productRepository.ReadOnlyRespository.GetAsync(
                    p => productCodes.Contains(p.Code));

                var vendorDict = vendors.ToDictionary(v => v.Code, v => v);
                var productDict = products.ToDictionary(p => p.Code, p => p);

                // Process valid import models
                foreach (var importModel in validImportModels)
                {
                    try
                    {
                        // Check if vendor and product exist
                        if (!vendorDict.TryGetValue(importModel.VendorCode, out var vendor))
                        {
                            result.FailedRows++;
                            result.Errors.Add(new ProductVendorExcelImportError
                            {
                                RowNumber = importModel.RowNumber,
                                VendorCode = importModel.VendorCode,
                                ProductCode = importModel.ProductCode,
                                ErrorMessages = new List<string> { $"Không tìm thấy nhà cung cấp với mã: {importModel.VendorCode}" }
                            });
                            continue;
                        }

                        if (!productDict.TryGetValue(importModel.ProductCode, out var product))
                        {
                            result.FailedRows++;
                            result.Errors.Add(new ProductVendorExcelImportError
                            {
                                RowNumber = importModel.RowNumber,
                                VendorCode = importModel.VendorCode,
                                ProductCode = importModel.ProductCode,
                                ErrorMessages = new List<string> { $"Không tìm thấy sản phẩm với mã: {importModel.ProductCode}" }
                            });
                            continue;
                        }

                        // Check if relationship already exists
                        var existingProductVendor = await _productVendorRepository.ReadOnlyRespository
                            .FirstOrDefaultAsync(pv => pv.Vendor_ID == vendor.ID && pv.Product_ID == product.ID);

                        if (existingProductVendor != null)
                        {
                            // Update existing relationship
                            existingProductVendor.UnitPrice = importModel.UnitPrice;
                            existingProductVendor.Priority = importModel.Priority;
                            existingProductVendor.Description = importModel.Description;

                            await _productVendorRepository.Repository.UpdateAsync(existingProductVendor);
                        }
                        else
                        {
                            // Create new relationship
                            var newProductVendor = new Product_Vendor
                            {
                                Vendor_ID = vendor.ID,
                                Product_ID = product.ID,
                                UnitPrice = importModel.UnitPrice,
                                Priority = importModel.Priority,
                                Description = importModel.Description
                            };

                            await _productVendorRepository.Repository.AddAsync(newProductVendor);
                        }

                        result.SuccessfulRows++;
                    }
                    catch (Exception ex)
                    {
                        result.FailedRows++;
                        result.Errors.Add(new ProductVendorExcelImportError
                        {
                            RowNumber = importModel.RowNumber,
                            VendorCode = importModel.VendorCode,
                            ProductCode = importModel.ProductCode,
                            ErrorMessages = new List<string> { $"Lỗi xử lý: {ex.Message}" }
                        });
                        _logger.LogError($"Error processing row {importModel.RowNumber}: {ex.Message}");
                    }
                }

                // Save changes
                await DbContext.SaveChangesAsync();

                response.Data = result;
                response.IsSuccess = true;
                return response;
            }
            catch (Exception ex)
            {
                response.ExtractMessage(ex);
                _logger.LogError($"ImportProductVendorsFromExcel: {ex.Message}");
                return response;
            }
        }

        public async Task<byte[]> GenerateProductVendorExcelTemplate()
        {
            try
            {
                using (var workbook = ExcelHelper.CreateWorkbook())
                {
                    // Create main data sheet
                    var dataSheet = workbook.Worksheets.Add("Hàng hóa - Nhà cung cấp");

                    // Set headers
                    var headers = new[]
                    {
                        "Mã nhà cung cấp (*)",
                        "Mã sản phẩm (*)",
                        "Đơn giá",
                        "Độ ưu tiên",
                        "Mô tả"
                    };

                    for (int i = 0; i < headers.Length; i++)
                    {
                        var cell = dataSheet.Cell(1, i + 1);
                        ExcelHelper.SetCellValue(cell, headers[i]);
                        cell.Style.Font.Bold = true;
                        cell.Style.Fill.BackgroundColor = XLColor.LightBlue;
                        cell.Style.Alignment.Horizontal = XLAlignmentHorizontalValues.Center;
                    }

                    // Auto-fit columns
                    dataSheet.Columns().AdjustToContents();

                    // Create sample data sheet
                    var sampleSheet = workbook.Worksheets.Add("Dữ liệu mẫu");

                    // Add sample headers
                    for (int i = 0; i < headers.Length; i++)
                    {
                        var cell = sampleSheet.Cell(1, i + 1);
                        ExcelHelper.SetCellValue(cell, headers[i]);
                        cell.Style.Font.Bold = true;
                        cell.Style.Fill.BackgroundColor = XLColor.LightBlue;
                        cell.Style.Alignment.Horizontal = XLAlignmentHorizontalValues.Center;
                    }

                    // Add sample data
                    var sampleData = new[]
                    {
                        new { VendorCode = "NCC001", ProductCode = "SP001", UnitPrice = "50000", Priority = "1", Description = "Nhà cung cấp chính" },
                        new { VendorCode = "NCC002", ProductCode = "SP001", UnitPrice = "52000", Priority = "2", Description = "Nhà cung cấp phụ" },
                        new { VendorCode = "NCC001", ProductCode = "SP002", UnitPrice = "75000", Priority = "1", Description = "" }
                    };

                    for (int i = 0; i < sampleData.Length; i++)
                    {
                        var row = i + 2;
                        ExcelHelper.SetCellValue(sampleSheet.Cell(row, 1), sampleData[i].VendorCode);
                        ExcelHelper.SetCellValue(sampleSheet.Cell(row, 2), sampleData[i].ProductCode);
                        ExcelHelper.SetCellValue(sampleSheet.Cell(row, 3), sampleData[i].UnitPrice);
                        ExcelHelper.SetCellValue(sampleSheet.Cell(row, 4), sampleData[i].Priority);
                        ExcelHelper.SetCellValue(sampleSheet.Cell(row, 5), sampleData[i].Description);
                    }

                    // Auto-fit columns
                    sampleSheet.Columns().AdjustToContents();

                    // Create instruction sheet
                    var instructionSheet = workbook.Worksheets.Add("Hướng dẫn");
                    var instructions = new[]
                    {
                        "HƯỚNG DẪN IMPORT HÀNG HÓA - NHÀ CUNG CẤP",
                        "",
                        "1. Các cột bắt buộc (có dấu *):",
                        "   - Mã nhà cung cấp: Mã của nhà cung cấp đã tồn tại trong hệ thống",
                        "   - Mã sản phẩm: Mã của sản phẩm đã tồn tại trong hệ thống",
                        "",
                        "2. Các cột tùy chọn:",
                        "   - Đơn giá: Giá đơn vị của sản phẩm từ nhà cung cấp",
                        "   - Độ ưu tiên: Số thứ tự ưu tiên (số nhỏ hơn = ưu tiên cao hơn)",
                        "   - Mô tả: Ghi chú về mối quan hệ nhà cung cấp - sản phẩm",
                        "",
                        "3. Lưu ý:",
                        "   - Nếu mối quan hệ đã tồn tại, hệ thống sẽ cập nhật thông tin",
                        "   - Nếu chưa tồn tại, hệ thống sẽ tạo mới",
                        "   - Mã nhà cung cấp và mã sản phẩm phải tồn tại trong hệ thống",
                        "   - Xem tab 'Dữ liệu mẫu' để tham khảo format"
                    };

                    for (int i = 0; i < instructions.Length; i++)
                    {
                        var cell = instructionSheet.Cell(i + 1, 1);
                        ExcelHelper.SetCellValue(cell, instructions[i]);
                        if (i == 0)
                        {
                            cell.Style.Font.Bold = true;
                            cell.Style.Font.FontSize = 14;
                        }
                    }

                    instructionSheet.Columns().AdjustToContents();

                    // Set the main data sheet as active
                    dataSheet.SetTabActive();

                    using (var stream = new MemoryStream())
                    {
                        workbook.SaveAs(stream, ExcelHelper.GetDefaultSaveOptions());
                        return stream.ToArray();
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError($"GenerateProductVendorExcelTemplate: {ex.Message}");
                throw;
            }
        }

        #region Helper Methods

        private static decimal? ParseDecimal(string value)
        {
            if (string.IsNullOrWhiteSpace(value))
                return null;

            if (decimal.TryParse(value.Replace(",", ""), out decimal result))
                return result;

            return null;
        }

        private static int? ParseInt(string value)
        {
            if (string.IsNullOrWhiteSpace(value))
                return null;

            if (int.TryParse(value, out int result))
                return result;

            return null;
        }

        private List<string> ValidateProductVendorImportModel(ProductVendorExcelImportModel model)
        {
            var errors = new List<string>();

            // Validate using data annotations
            var validationContext = new ValidationContext(model);
            var validationResults = new List<ValidationResult>();

            if (!Validator.TryValidateObject(model, validationContext, validationResults, true))
            {
                errors.AddRange(validationResults.Select(vr => vr.ErrorMessage ?? "Validation error"));
            }

            // Additional business validations
            if (model.Priority.HasValue && model.Priority.Value < 0)
            {
                errors.Add("Độ ưu tiên phải là số không âm");
            }

            if (model.UnitPrice.HasValue && model.UnitPrice.Value < 0)
            {
                errors.Add("Đơn giá phải là số không âm");
            }

            return errors;
        }

        #endregion

    }
}
