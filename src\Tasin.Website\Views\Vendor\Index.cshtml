﻿@{
    ViewData["Title"] = "Nhà cung cấp";
}

@section Styles {
    <link href="~/css/kendo-grid-common.css" rel="stylesheet" asp-append-version="true" />
    <link href="~/css/toolbar-common.css" rel="stylesheet" asp-append-version="true" />
    @* <link href="~/css/vendor-form.css" rel="stylesheet" /> *@
    <link href="~/css/vendor-mobile.css" rel="stylesheet" asp-append-version="true" />
}

<div>

    <h4 class="demo-section wide title">@ViewData["Title"]</h4>
    @*   <div class="demo-section wide title">
    <h4 class="demo-section wide title">@ViewData["Title"]</h4>
    <nav id="breadcrumb"></nav>
    </div> *@
    <div id="divContent">
        <div id="gridId">
        </div>
    </div>
    <div id="window"></div>
    <div id="dialog"></div>
</div>

<script type="text/javascript">
    let gridId = "#gridId";
    let ListProduct_ID = [];


    function addVendorProduct(vendorId) {
        let myWindow = $("#window");
        $("#window").html(`
            <div class="product-vendor-popup">
                <div class="search-container">
                    <div class="row mb-3 gx-0">
                        <div class="col-md-12">
                            <div class="search-input-wrapper">
                                <input type="text" id="productSearchInput" class="search-input"
                                       placeholder="Nhập tên sản phẩm hoặc mô tả để tìm kiếm..." />
                            </div>
                        </div>
                    </div>
                </div>
                <div class="grid-container">
                    <div id='productVendorGrid'></div>
                </div>
            </div>
        `);

        $("#productSearchInput").kendoTextBox({});
        let title = "THÊM HÀNG HOÁ CHO NHÀ CUNG CẤP";

        // Function to initialize the grid with data
        function initializeGrid(productVendorData) {
            // Initialize grid for adding multiple products
            $("#productVendorGrid").kendoGrid({
                dataSource: {
                    data: productVendorData,
                    schema: {
                        model: {
                            id: "Product_ID",
                            fields: {
                                Product_ID: { type: "number", validation: { required: true } },
                                ProductName: { type: "string", editable: false },
                                Price: { type: "number" },
                                UnitPrice: { type: "number" },
                                Priority: { type: "number" },
                                Description: { type: "string" }
                            }
                        },
                        parse: function (response) {
                            ListProduct_ID = response.map(e => e.Product_ID);
                            return response;
                        },
                    }
                },
                height: "100%",
                scrollable: true,
                sortable: true,
                filterable: false,
                resizable: true,
                editable: {
                    mode: "incell",
                    createAt: "top"
                },
                toolbar: [
                    {
                        name: "create", text: "Thêm sản phẩm", iconClass: "k-icon k-i-plus",
                        className: "k-button k-button-md k-rounded-md k-button-solid k-button-solid-success"
                    },
                    {
                        name: "save", text: "Lưu tất cả", iconClass: "k-icon k-i-save",
                        className: "k-button k-button-md k-rounded-md k-button-solid k-button-solid-primary"
                    },
                    {
                        name: "cancel", text: "Hủy", iconClass: "k-icon k-i-cancel",
                        className: "k-button k-button-md k-rounded-md k-button-solid k-button-solid-error"
                    }
                ],
                columns: [
                    {
                        field: "Product_ID",
                        title: "Sản phẩm (*)",
                        width: 250,
                        headerAttributes: { style: "text-align: center; font-weight: 600;" },
                        attributes: { style: "text-align: left;" },
                        editor: function (container, options) {
                            $('<input required data-bind="value:' + options.field + '"/>')
                                .appendTo(container)
                                .kendoDropDownList({
                                    dataTextField: "name",
                                    dataValueField: "id",
                                    filter: filterCustom,
                                    dataSource: {
                                        transport: {
                                            read: {
                                                url: "/Product/GetProductList",
                                                datatype: "json",
                                            },
                                            parameterMap: function (data, type) {
                                                if (type == "read") {
                                                    return {
                                                        pageSize: 100,
                                                        pageNumber: 1,
                                                        processingType: "Material"
                                                    }
                                                }
                                            },
                                        },
                                        serverPaging: true,
                                        serverFiltering: true,
                                        pageSize: 100,
                                        schema: {
                                            type: 'json',
                                            parse: function (response) {
                                                if (response.isSuccess == false) {
                                                    showErrorMessages(response.errorMessageList);
                                                    return { data: [], total: 0 }
                                                }
                                                // return response.data.data.filter(e=> !ListProduct_ID.includes(e.id));
                                                return response.data;
                                            },
                                            model: {
                                                id: "id",
                                                fields: {
                                                    name: { type: "string" },
                                                    code: { type: "string" }
                                                }
                                            },
                                            data: "data",
                                            total: "total"
                                        },
                                    },
                                    change: function (e) {
                                        var selectedItem = this.dataItem();
                                        if (selectedItem) {
                                            var grid = $("#productVendorGrid").data("kendoGrid");
                                            var dataItem = grid.dataItem(container.closest("tr"));
                                            dataItem.set("ProductName", selectedItem.name);
                                            dataItem.ProductName = selectedItem.name;
                                            ListProduct_ID.push(selectedItem.id);
                                        }
                                    }
                                });
                        },
                        // template: "#= ProductName || '' #"
                        template: function (dataItem) {

                            return dataItem.ProductName || "";
                        }
                    },

                    {
                        field: "UnitPrice",
                        title: "Đơn giá",
                        width: 130,
                        format: "{0:n0}",
                        headerAttributes: { style: "text-align: center; font-weight: 600;" },
                        attributes: { style: "text-align: right;" },
                        editor: function (container, options) {
                            $('<input data-bind="value:' + options.field + '"/>')
                                .appendTo(container)
                                .kendoNumericTextBox({
                                    format: "n0",
                                });
                        }
                    },
                    {
                        field: "Priority",
                        title: "Ưu tiên",
                        width: 110,
                        headerAttributes: { style: "text-align: center; font-weight: 600;" },
                        attributes: { style: "text-align: center;" },
                        editor: function (container, options) {
                            $('<input data-bind="value:' + options.field + '"/>')
                                .appendTo(container)
                                .kendoNumericTextBox({
                                    format: "n0",
                                    min: 1
                                });
                        }
                    },
                    {
                        field: "Description",
                        title: "Mô tả",
                        width: 200,
                        headerAttributes: { style: "text-align: center; font-weight: 600;" },
                        attributes: { style: "text-align: left;" }
                    },
                    {
                        command: [
                            { name: "destroy", text: "Xoá", className: "k-button k-button-md k-rounded-md k-button-solid k-button-solid-error" }
                        ],

                        title: "Thao tác",
                        width: 120,
                        headerAttributes: { style: "text-align: center; font-weight: 600;" },
                        attributes: { style: "text-align: center;" }
                    }
                ],
                save: function (e) {
                    // Handle save event for individual rows
                }
            });

            // Handle toolbar save button
            $("#productVendorGrid").on("click", ".k-grid-save-changes", function (e) {
                e.preventDefault();
                saveAllProductVendors(vendorId);
            });

            // Handle search functionality with debounce for better performance
            let searchTimeout;
            $("#productSearchInput").on("input", function () {
                clearTimeout(searchTimeout);

                // Show loading state
                $("#searchResultCount").html('<i class="fas fa-spinner fa-spin"></i> Đang tìm kiếm...');
                $("#searchResultCount").addClass('search-loading');

                searchTimeout = setTimeout(() => {
                    var searchValue = $(this).val().trim();
                    var grid = $("#productVendorGrid").data("kendoGrid");

                    if (searchValue === "") {
                        grid.dataSource.filter({});
                    } else {
                        // Search in multiple fields: ProductName and Description (case-insensitive)
                        grid.dataSource.filter({
                            logic: "or",
                            filters: [
                                {
                                    field: "ProductName",
                                    operator: filterCustom,
                                    value: searchValue,
                                    ignoreCase: true
                                },
                                {
                                    field: "Description",
                                    operator: filterCustom,
                                    value: searchValue,
                                    ignoreCase: true
                                }
                            ]
                        });
                    }
                }, 300); // 300ms debounce
            });

            // Handle Enter key for search
            $("#productSearchInput").on("keypress", function (e) {
                if (e.which === 13) { // Enter key
                    e.preventDefault();
                    $(this).trigger("input");
                }
            });

            function remove() {
                setTimeout(() => {
                    if ($(".k-window #window").length > 0) {
                        $("#window").parent().remove();
                        $(gridId).after("<div id='window'></div>");
                    }
                }, 200);
            }

            myWindow.kendoWindow({
                width: "900px",
                height: "600px",
                title: title,
                visible: false,
                actions: ["Close"],
                resizable: true,
                draggable: true,
                modal: true,
                close: function (e) {
                    remove();
                },
            }).data("kendoWindow").center();
            myWindow.data("kendoWindow").open();
        }

        // Load existing products for this vendor
        ajax("GET", "/ProductVendor/GetProductsByVendorId/" + vendorId, null, (response) => {
            let productVendorData = [];
            if (response.isSuccess && response.data) {
                // Convert API response to grid format
                productVendorData = response.data.map(item => ({
                    Product_ID: item.product_ID,
                    ProductName: item.productName,
                    UnitPrice: item.unitPrice,
                    Priority: item.priority,
                    Description: item.description
                }));
            }
            initializeGrid(productVendorData);
        }, (error) => {
            // Error callback - initialize grid with empty data if API call fails
            initializeGrid([]);
        }, false); // End of ajax call
    }

    function saveAllProductVendors(vendorId) {
        var grid = $("#productVendorGrid").data("kendoGrid");
        var data = grid.dataSource.data();

        if (data.length === 0) {
            showErrorMessages(["Vui lòng thêm ít nhất một sản phẩm."]);
            return;
        }

        // Validate required fields
        var hasErrors = false;
        var products = [];

        for (var i = 0; i < data.length; i++) {
            var item = data[i];
            if (!item.Product_ID) {
                showErrorMessages(["Vui lòng chọn sản phẩm cho tất cả các dòng."]);
                hasErrors = true;
                break;
            }

            products.push({
                Product_ID: item.Product_ID,
                UnitPrice: item.UnitPrice || 0,
                Priority: item.Priority || 1,
                Description: item.Description || ""
            });
        }

        if (hasErrors) return;

        var requestData = {
            VendorId: vendorId,
            Products: products
        };

        var response = ajax("POST", "/ProductVendor/BulkAddProductsToVendor", requestData, () => {
            $(gridId).data("kendoGrid").dataSource.filter({});
            $("#window").data("kendoWindow").close();
            showSuccessMessages(["Thêm sản phẩm thành công!"]);
        }, null, false);
    }

    function renderCreateOrEditForm(isCreate = true, dataVendor = {}) {
        let myWindow = $("#window");
        $("#window").html("<form id='formCreateAndEdit'></form>");

        let formData = {
            id: 0,
            name: "",
            address: "",
            email: "",
            taxCode: "",
            phoneNumber: "",
            // status: "",
            ...dataVendor
        };
        let strSubmit = "Thêm";
        let title = "THÊM MỚI"
        let element;
        if (isCreate == false) {
            strSubmit = "Sửa";
            title = "CẬP NHẬT";
        }
        $("#formCreateAndEdit").kendoForm({
            orientation: "vertical",
            formData: formData,
            type: "group",
            items: [

                {
                    field: "name",
                    title: "Họ tên",
                    label: "Họ tên (*):",
                    validation: {
                        validationMessage: "Vui lòng nhập họ tên",
                        required: true
                    },
                },
                {
                    field: "address",
                    title: "Địa chỉ",
                    label: "Địa chỉ:",
                    validation: {
                        address: true
                    },
                },
                {
                    field: "email",
                    title: "Email",
                    label: "Email:",
                    validation: {
                        email: true
                    },
                },
                {
                    field: "taxCode",
                    title: "Mã số thuế",
                    label: "Mã số thuế:",
                    validation: {
                        taxCode: function (input) {
                            if (input.val() && input.val().trim()) {
                                var cleanTaxCode = input.val().replace(/[\s-]/g, '');
                                if (cleanTaxCode.length < 10 || cleanTaxCode.length > 13) {
                                    return "Mã số thuế phải có từ 10-13 ký tự";
                                }
                                if (!/^\d+$/.test(cleanTaxCode)) {
                                    return "Mã số thuế chỉ được chứa số";
                                }
                            }
                            return true;
                        }
                    },
                },
                {
                    field: "phoneNumber",
                    title: "Số điện thoại",
                    label: "Số điện thoại:",
                    validation: {
                        phoneNumber: function (input) {
                            if (input.val() && input.val().trim()) {
                                var cleanPhone = input.val().replace(/[\s\-\(\)\+]/g, '').replace(/^\+84/, '0');
                                if (cleanPhone.length < 10 || cleanPhone.length > 11) {
                                    return "Số điện thoại phải có từ 10-11 ký tự";
                                }
                                if (!/^\d+$/.test(cleanPhone)) {
                                    return "Số điện thoại chỉ được chứa số";
                                }
                                if (!cleanPhone.startsWith('0')) {
                                    return "Số điện thoại phải bắt đầu bằng số 0";
                                }
                            }
                            return true;
                        }
                    },
                },

            ],
            messages: {
                submit: strSubmit, clear: "Đặt lại"
            },
            submit: function (e) {
                e.preventDefault();
                let dataItem = {
                    ...formData,
                    ...e.model,
                };

                if (dataItem.id > 0) {
                    var response = ajax("PUT", "/Vendor/UpdateVendor/" + dataItem.id, dataItem, () => {
                        $(gridId).data("kendoGrid").dataSource.filter({});
                        myWindow.data("kendoWindow").close();
                    });
                }
                else {
                    var response = ajax("POST", "/Vendor/Create", dataItem, () => {
                        $(gridId).data("kendoGrid").dataSource.filter({});
                        myWindow.data("kendoWindow").close();
                    });
                }
            },
            close: function (e) {
                $(this.element).empty();
            },
        });
        if (!isCreate) {

        }

        // if (Userdata.roleIdList?.includes(ERoleType.Admin) == false) {
        //     $("#userName").data("kendoTextBox").enable(false);
        // }


        setTimeout(() => {
            $("input[title='name']").focus();
        }, 500);

        function remove() {
            setTimeout(() => {
                if ($(".k-window #window").length > 0) {
                    $("#window").parent().remove();
                    $(gridId).after("<div id='window'></div>");
                }
            }, 200)
        }

        myWindow.kendoWindow({
            width: "500px",
            // height: "50vh",
            title: "",
            visible: false,
            actions: ["Close"],
            resizable: false,
            draggable: false,
            modal: true,
            close: function (e) {
                //$("#window").empty();
                remove();
            },
        }).data("kendoWindow").title(title).center();
        myWindow.data("kendoWindow").open();
    }

    async function editVendor(id) {
        var response = ajax("GET", "/Vendor/GetVendorById/" + id, {}, (response) => {
            renderCreateOrEditForm(false, response.data);
        }, null, false);
    }
    function deleteVendor(id) {
        $('#dialog').kendoConfirm({
            title: "THÔNG BÁO XÓA NHÀ CUNG CẤP",
            content: "Bạn có chắc chắn xóa nhà cung cấp này không?",
            size: "medium",
            messages: {
                okText: "Đồng ý",
                cancel: "Hủy"

            },
        }).data("kendoConfirm").open().result.done(function () {
            var response = ajax("DELETE", "/Vendor/DeleteVendorById/" + id, {
                vendorId: id
            }, () => {
                $(gridId).data("kendoGrid").dataSource.filter({});
            });
        })

        $("#window").after("<div id='dialog'></div>");
    }


    async function ExportExcel() {
        let dataSheet1 = [
            {
                cells: [
                    {
                        value: "Mã nhà cung cấp", textAlign: "center", background: "#428dd8"
                    },
                    {
                        value: "Họ tên", textAlign: "center", background: "#428dd8"
                    },
                    {
                        value: "Địa chỉ", textAlign: "center", background: "#428dd8"
                    },
                ]
            }];

        var searchModel = getSearchModel();
        let postData = {
            ...searchModel,
            pageSize: 999999999,
            pageNumber: 1
        }
        let dataSourceVendor = null;
        var response = await ajax("GET", "/Vendor/GetVendorList", postData, (urnResponse) => {
            dataSourceVendor = urnResponse.data.data;
        }, null, false);
        if (dataSourceVendor == null) return;

        for (let index = 0; index < dataSourceVendor.length; index++) {
            dataSheet1.push({
                cells: [
                    { value: dataSourceVendor[index].code },
                    { value: dataSourceVendor[index].name },
                    { value: dataSourceVendor[index].address },
                ]
            })
        }


        var workbook = new kendo.ooxml.Workbook({
            sheets: [
                {
                    name: "Danh sách nhà cung cấp",
                    columns: [
                        { width: 200 }, { autoWidth: true }, { autoWidth: true },
                        { autoWidth: true }, { autoWidth: true }, { autoWidth: true }
                    ],
                    rows: dataSheet1,
                }
            ]
        });
        kendo.saveAs({
            dataURI: workbook.toDataURL(),
            fileName: "Danh sách nhà cung cấp _ " + kendo.toString(new Date(), "dd_MM_yyyy__HH_mm_ss") + ".xlsx"
        });
    }

    // Download ProductVendor Excel template
    function downloadProductVendorTemplate() {
        window.open('/ProductVendor/ExcelTemplate', '_blank');
    }

    // Show ProductVendor import dialog
    function showProductVendorImportDialog() {
        const dialogHtml = `
            <div id="productVendorImportDialog">
                <div class="k-form-layout" style="grid-template-columns: 1fr;">
                    <div class="k-form-field">
                        <label class="k-label k-form-label">Chọn file Excel chứa danh sách hàng hóa - nhà cung cấp:</label>
                        <div class="k-form-field-wrap">
                            <input type="file" id="productVendorImportFile" accept=".xlsx,.xls,.xlsm,.csv" class="k-textbox" />
                            <div class="k-form-hint">Chỉ chấp nhận file Excel (.xlsx, .xls, .xlsm) hoặc CSV. Vui lòng tải file mẫu để xem định dạng.</div>
                        </div>
                    </div>
                    <div class="k-form-field">
                        <div class="k-form-field-wrap">
                            <div id="productVendorImportProgress" style="display: none;">
                                <div class="k-progressbar">
                                    <div class="k-progress-status-wrap">
                                        <span class="k-progress-status">Đang xử lý...</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="k-form-field">
                        <div class="k-form-field-wrap">
                            <div id="productVendorImportResult" style="display: none;">
                                <div class="alert alert-info">
                                    <h6>Kết quả import:</h6>
                                    <div id="productVendorImportSummary"></div>
                                    <div id="productVendorImportErrors" style="display: none;">
                                        <h6 class="mt-3">Chi tiết lỗi:</h6>
                                        <div id="productVendorErrorList"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;

        const dialog = $(dialogHtml).kendoDialog({
            width: "600px",
            title: "Import danh sách hàng hóa - nhà cung cấp",
            closable: true,
            modal: true,
            actions: [
                {
                    text: "Hủy",
                    action: function (e) {
                        return true; // Close dialog
                    }
                },
                {
                    text: "Bắt đầu Import",
                    primary: true,
                    action: function (e) {
                        const fileInput = document.getElementById('productVendorImportFile');
                        if (fileInput.files.length === 0) {
                            notification.show({
                                title: "Cảnh báo!",
                                message: "Vui lòng chọn file Excel để import!"
                            }, "info");
                            return false; // Don't close dialog
                        }

                        importProductVendorExcelFile(fileInput.files[0]);
                        return false; // Don't close dialog yet
                    }
                }
            ]
        }).data("kendoDialog");

        dialog.open();
    }

    // Import ProductVendor Excel file
    function importProductVendorExcelFile(file) {
        const formData = new FormData();
        formData.append('file', file);

        // Show progress
        $("#productVendorImportProgress").show();
        $("#productVendorImportResult").hide();

        $.ajax({
            url: '/ProductVendor/ImportExcel',
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function (response) {
                $("#productVendorImportProgress").hide();

                if (response.isSuccess && response.data) {
                    const result = response.data;

                    // Show summary
                    const summaryHtml = `
                        <p><strong>Tổng số dòng:</strong> ${result.totalRows}</p>
                        <p><strong>Thành công:</strong> <span class="text-success">${result.successfulRows}</span></p>
                        <p><strong>Thất bại:</strong> <span class="text-danger">${result.failedRows}</span></p>
                    `;
                    $("#productVendorImportSummary").html(summaryHtml);

                    // Show errors if any
                    if (result.errors && result.errors.length > 0) {
                        let errorsHtml = '<div class="table-responsive"><table class="table table-sm table-striped"><thead><tr><th>Dòng</th><th>Mã NCC</th><th>Mã SP</th><th>Lỗi</th></tr></thead><tbody>';
                        result.errors.forEach(error => {
                            errorsHtml += `<tr>
                                <td>${error.rowNumber}</td>
                                <td>${error.vendorCode}</td>
                                <td>${error.productCode}</td>
                                <td>${error.errorMessages.join(', ')}</td>
                            </tr>`;
                        });
                        errorsHtml += '</tbody></table></div>';
                        $("#productVendorErrorList").html(errorsHtml);
                        $("#productVendorImportErrors").show();
                    } else {
                        $("#productVendorImportErrors").hide();
                    }

                    $("#productVendorImportResult").show();

                    // Refresh grid if successful
                    if (result.successfulRows > 0) {
                        $(gridId).data("kendoGrid").dataSource.filter({});
                        notification.show({
                            title: "Thành công!",
                            message: `Import thành công ${result.successfulRows} dòng dữ liệu!`
                        }, "success");
                    }
                } else {
                    notification.show({
                        title: "Lỗi!",
                        message: response.errorMessageList ? response.errorMessageList.join(', ') : "Có lỗi xảy ra khi import file!"
                    }, "error");
                }
            },
            error: function (xhr, status, error) {
                $("#productVendorImportProgress").hide();
                notification.show({
                    title: "Lỗi!",
                    message: "Có lỗi xảy ra khi upload file: " + error
                }, "error");
            }
        });
    }

    function getSearchModel() {
        let searchString = $("#searchString").val();

        return {
            searchString,
        };
    }
    function InitGrid() {
        let htmlToolbar = `
                <div id='toolbar' style=''  class='w-100 d-flex flex-column'>
                       <div class="row gx-0 row-gap-2 w-100">
                            <div class="col-xl-3 col-lg-4 col-md-6 col-sm-6 col-12">
                                <div class="pe-1">
                                    <label for="searchString">Tìm kiếm:</label>
                                    <input type="text" class="w-100" id="searchString"/>
                                </div>
                            </div>
                            <div class="col-xl-9 col-lg-8 col-md-6 col-sm-6 col-12 d-flex align-items-end">
                                <div class="pe-1 d-flex gap-2 flex-wrap">
                                    <button id="search" title="Tìm kiếm" class="k-button k-button-md k-rounded-md k-button-solid k-button-solid-primary k-icon-button"><span class='k-icon k-i-search k-button-icon'></span><span class='k-button-text d-none'>Tìm kiếm</span></button>
                                    <button id='create' title="Thêm" class='k-button k-button-md k-rounded-md k-button-solid k-button-solid-success _permission_' data-enum='16'><span class='k-icon k-i-plus k-button-icon'></span><span class='k-button-text'>Thêm</span></button>
                                    <button id="downloadTemplate" title="Tải file mẫu import hàng hóa - nhà cung cấp" class="k-button k-button-md k-rounded-md k-button-outline k-button-outline-info _permission_" data-enum="4"><span class="k-icon k-i-download k-button-icon"></span><span class="k-button-text">Tải file mẫu</span></button>
                                    <button id="importExcel" title="Import danh sách hàng hóa - nhà cung cấp từ Excel" class="k-button k-button-md k-rounded-md k-button-solid k-button-solid-info _permission_" data-enum="16"><span class="k-icon k-i-upload k-button-icon"></span><span class="k-button-text">Import Excel</span></button>
                                    <button id="exportExcel" class="k-button k-button-md k-rounded-md k-button-outline k-button-outline-error"><span class="k-icon k-i-file-excel k-button-icon"></span><span class="k-button-text">Export Excel</span></button>
                                </div>
                            </div>
                        </div>
                </div>
            `;

        $(gridId).kendoGrid({
            dataSource: {
                transport: {
                    read: {
                        url: "/Vendor/GetVendorList",
                        datatype: "json",
                    },
                    parameterMap: function (data, type) {
                        if (type == "read") {
                            var searchModel = getSearchModel();
                            return {
                                ...searchModel,
                                pageSize: data.pageSize,
                                pageNumber: data.page
                            }
                        }

                    },
                },
                serverPaging: true,
                serverFiltering: true,
                page: 1,
                pageSize: 20,
                schema: {
                    type: 'json',
                    parse: function (response) {
                        if (response.isSuccess == false) {
                            showErrorMessages(response.errorMessageList);
                            return {
                                data: [],
                                total: 0
                            }
                        }
                        return response.data;
                    },
                    model: {
                        id: "id",
                        fields: {
                            createdDate: { type: "date" },
                            updatedDate: { type: "date" },
                            stt: { type: "number" },

                        }
                    },
                    data: "data",
                    total: "total"
                },
            },
            selectable: true,
            scrollable: {
                virtual: false
            },
            pageable: {
                pageSizes: [10, 20, 50],
            },
            dataBinding: function (e) {
                record = (this.dataSource._page - 1) * this.dataSource._pageSize;
            },
            toolbar: htmlToolbar,
            // toolbar: "<div id='toolbar' style='width:100%'></div><div class='report-toolbar'>\</div>",
            columns: [
                {
                    field: "",
                    title: "STT",
                    headerAttributes: { style: "text-align: center; justify-content: center" },
                    attributes: { style: "text-align:center;" },
                    template: "#: ++record #",
                    width: 100
                },
                {
                    field: "code",
                    title: "Mã nhà cung cấp",
                    headerAttributes: { style: "text-align: center; justify-content: center" },
                    attributes: { style: "text-align:center;" },
                    width: 200,
                },
                {
                    field: "name",
                    title: "Họ tên",
                    headerAttributes: { style: "text-align: center; justify-content: center" },
                    attributes: { style: "text-align:center;" },
                    width: 200,
                },
                {
                    field: "address",
                    title: "Địa chỉ",
                    width: 200,
                    headerAttributes: { style: "text-align: center; justify-content: center" },
                    attributes: { style: "text-align:center;" },
                },
                {
                    field: "email",
                    title: "Email",
                    width: 200,
                    headerAttributes: { style: "text-align: center; justify-content: center" },
                    attributes: { style: "text-align:center;" },
                },
                {
                    field: "taxCode",
                    title: "Mã số thuế",
                    width: 150,
                    headerAttributes: { style: "text-align: center; justify-content: center" },
                    attributes: { style: "text-align:center;" },
                },
                {
                    field: "phoneNumber",
                    title: "Số điện thoại",
                    width: 150,
                    headerAttributes: { style: "text-align: center; justify-content: center" },
                    attributes: { style: "text-align:center;" },
                },
                {
                    field: "updatedDate",
                    title: "Ngày cập nhật",
                    width: 200,
                    headerAttributes: { style: "text-align: center; justify-content: center" },
                    attributes: { style: "text-align:center;" },
                    template: '#: kendo.toString(kendo.parseDate(updatedDate || createdDate), "dd/MM/yyyy HH:mm:ss")#',
                },
                {
                    field: "updatedByName",
                    title: "Người cập nhật",
                    width: 150,
                    headerAttributes: { style: "text-align: center; justify-content: center" },
                    attributes: { style: "text-align:center;" },
                },
                {
                    field: "", title: "Thao tác", width: 200, attributes: { style: "text-align: center;" },
                    headerAttributes: { style: "text-align: center; justify-content: center" },
                    template: function (dataItem) {
                        return '<div class="action-buttons">' +
                            '<button onclick="addVendorProduct(' + dataItem.id + ')" title="Thêm hàng hoá" class="btn-action btn-add _permission_" data-enum="4">' +
                            '<i class="fas fa-plus"></i>' +
                            '</button>' +
                            '<button onclick="editVendor(' + dataItem.id + ')" title="Chỉnh sửa" class="btn-action btn-edit _permission_" data-enum="6">' +
                            '<i class="fas fa-edit"></i>' +
                            '</button>' +
                            '<button onclick="deleteVendor(' + dataItem.id + ')" title="Xoá" class="btn-action btn-delete _permission_" data-enum="7">' +
                            '<i class="fas fa-trash"></i>' +
                            '</button>' +
                            '</div>';
                    }
                }
            ],
            dataBound: function (e) {
                CheckPermission();
                // Force horizontal scroll on mobile
                if (window.innerWidth <= 768) {
                    setTimeout(function() {
                        $('#gridId').css({
                            'overflow-x': 'auto',
                            'min-width': '1200px'
                        });
                        $('#gridId .k-grid-header, #gridId .k-grid-content').css({
                            'overflow-x': 'auto',
                            'min-width': '1200px'
                        });
                    }, 100);
                }
            }
        });


    }
    function InitKendoToolBar() {

        $("#search").kendoButton({
            icon: "search"
        });
        $("#search").click(async function (e) {
            var grid = $(gridId).data("kendoGrid");
            grid.dataSource.filter({});
        });
        $("#exportExcel").click(async function (e) {
            ExportExcel();
        });
        $("#searchString").kendoTextBox({
            icon: {
                type: "search",
                position: "end"  // Có thể là "start" hoặc "end"
            },
            placeholder: "Nhập từ khóa tìm kiếm..."
        });
        $("#create").kendoButton({
            icon: "plus"
        });

        $("#export").click(async function (e) {
            let grid = $(gridId).data("kendoGrid");
            grid.saveAsExcel();
        });


        $("#create").on('click', function () {
            renderCreateOrEditForm();
        });

        $("#downloadTemplate").kendoButton({
            icon: "download"
        });
        $("#downloadTemplate").click(function (e) {
            downloadProductVendorTemplate();
        });

        $("#importExcel").kendoButton({
            icon: "upload"
        });
        $("#importExcel").click(function (e) {
            showProductVendorImportDialog();
        });

    };

</script>
<script type="text/javascript">
    InitGrid();
    InitKendoToolBar();
    $(document).ready(function () {
        $(window).trigger("resize");

    });
</script>
<style>
    .k-form-buttons {
        justify-content: flex-end;
    }

    /* Add product button styling */
    .btn-add {
        background: #28a745;
        color: white;
    }

    .btn-add:hover {
        background: #218838;
    }

    /* Force grid horizontal scroll for mobile */
    @media (max-width: 768px) {
        #divContent {
            overflow-x: auto !important;
            -webkit-overflow-scrolling: touch;
            width: 100%;
        }

        #gridId.k-grid {
            overflow-x: auto !important;
            -webkit-overflow-scrolling: touch;
            min-width: 1200px !important; /* Force minimum width */
            width: auto !important;
        }

        #gridId .k-grid-header {
            overflow-x: auto !important;
            min-width: 1200px !important;
        }

        #gridId .k-grid-content {
            overflow-x: auto !important;
            min-width: 1200px !important;
        }

        /* Force table to maintain width */
        #gridId .k-grid-header table,
        #gridId .k-grid-content table {
            min-width: 1200px !important;
            width: auto !important;
        }

        /* Ensure columns don't shrink */
        #gridId .k-grid-header th,
        #gridId .k-grid-content td {
            white-space: nowrap !important;
            min-width: 100px !important;
        }
    }
</style>
