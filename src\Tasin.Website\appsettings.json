{
  "ConnectionStrings": {
       // "TasinDB": "Server=*************;Port=15432;Database=TasinDB;User Id=postgres;Password=*****************;",
     "TasinDB": "Server=localhost;Port=5432;Database=TasinDB;User Id=postgres;Password=******;"
    //"TasinDB": "Server=ep-white-leaf-a85po2ip-pooler.eastus2.azure.neon.tech;Port=5432;Database=neondb;User Id=neondb_owner;Password=****************;Ssl Mode=Require;Trust Server Certificate=true;Options=endpoint=ep-white-leaf-a85po2ip-pooler;"
  },
  "Logging": {
    "LogLevel": {
      "Default": "Trace",
      "Microsoft": "Warning",
      "Microsoft.Hosting.Lifetime": "Information",
      "Microsoft.EntityFrameworkCore.Database.Command": "Information"
    },
    "DetailedErrors": true
  },
  "TimeRunTelegramNotiHour": 8,
  "TimeRunTelegramNotiMinute": 0,
  "NumberOfDaysNotiGeneralDay": 125,
  "TelegramBotUrl": "http://t.me/LinhCotBot",
  "AllowedHosts": "*",
  "DefaultResetPassword": "******",
  "TelegramToken": "**********************************************",
  "BackgroundService": "true",
  "Path": {
    "WebsiteInfo": {
      "NameWebsite": "Phần mềm quản lý đơn hàng",
      "SiteUITitleFooter": "Copyright 2025",
      "SiteUILoginBackgroundUrl": "../content/images/bg.jpg",
      //"SiteUILogin": "../content/images/logoLogin.png",
      //"SiteUILogoUrl": "../content/images/logo.png",
      "SiteUILogin": "../content/images/logoSatra.png",
      "SiteUILogoUrl": "../content/images/f.png"

    },
    "HostUsing": {
      "MediaFTP": "https://*********:1400/"
    },
    "ELASTICS": {
      "BaseUrl": "",
      "Username": "elastic",
      "Password": ""
    },
    "ChatHubUrl": "/chatHub",
    "AuthCookie_": "Tasin.AuthCookie_"
  },
  "FolderPathImage": "C:/FTP",
  "FTP": {
    "MediaServer": "/Files/",
    "MediaServerWithLowQuality": "/FilesWithLowQuality/",
    "Host": "ftp://*********",
    "Port": 21,
    "UserName": "/Q452Dq4IjO/TgONwOBjow==",
    "Password": "GuJE+ncmUC9vE7545o0Esw=="
    //"Port": 21,
    //"UserName": "ZawDxgP0USp9kcH3WBKeGg==",
    //"Password": "tL5tQ6GrwhGGNZoBLATx5g=="
  },
  "profiles": {
    "IIS Express": {
      "commandName": "IISExpress",
      "launchBrowser": true,
      "environmentVariables": {
        "ASPNETCORE_ENVIRONMENT": "Development"
      }
    }
  },
  "JWT": {
    "SecretKey": "$2a$10$v9n9Osb00.qmmslF0FOboOO4PP4NG5Cv8lDIFgOnLsyvCPpQUKaUG"
  },
  "EmailSettings": {
    "SmtpServer": "smtp.gmail.com",
    "SmtpPort": 587,
    "SmtpUser": "<EMAIL>",
    "SmtpPass": "pobl oyid atvm tacm",
    "FromEmail": "<EMAIL>",
    "FromName": "Phần mềm Tasin"
  },
  "NumberOfDaysNoticeAnniversary": 10,
  "NumberOfDaysNoticeExpiredUrn": 5,
  "RemindNotification": 2,
  "DayGeneralNotification": 3,
  "MonthGeneralNotification": 15,
  "ReminderEmailSubject": "NHẮC NHỠ ĐẾN NGÀY",
  //"UrlWeb": "https://localhost:44350/"
  "UrlWeb": "http://***************:28068/"

}
