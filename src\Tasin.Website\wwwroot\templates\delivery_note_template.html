<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Phiếu giao hàng {{DeliveryNoteCode}}</title>
    <style>
        @page {
            size: A4;
            margin: 15mm;
        }
        
        @media print {
            body { 
                margin: 0; 
                -webkit-print-color-adjust: exact;
                color-adjust: exact;
            }
            .no-print { 
                display: none !important; 
            }
            .page-break {
                page-break-before: always;
            }
        }
        
        * {
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Times New Roman', serif;
            font-size: 13px;
            line-height: 1.4;
            color: #000;
            background: #fff;
            margin: 0;
            padding: 0;
        }
        
        .delivery-container {
            max-width: 210mm;
            margin: 0 auto;
            padding: 10mm;
            background: white;
            min-height: 297mm;
        }
        
        .delivery-header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #333;
            padding-bottom: 20px;
        }
        
        .company-info {
            text-align: left;
            margin-bottom: 20px;
        }
        
        .company-name {
            font-size: 18px;
            font-weight: bold;
            color: #2c5aa0;
            margin-bottom: 8px;
        }
        
        .company-details {
            font-size: 12px;
            line-height: 1.6;
        }
        
        .delivery-title {
            font-size: 24px;
            font-weight: bold;
            color: #d63384;
            margin: 20px 0 10px 0;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        
        .delivery-number {
            font-size: 16px;
            font-weight: bold;
            color: #666;
            margin-bottom: 10px;
        }
        
        .delivery-details {
            display: flex;
            justify-content: space-between;
            margin-bottom: 30px;
            gap: 30px;
        }
        
        .delivery-info, .customer-info {
            flex: 1;
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #d63384;
        }
        
        .info-label {
            font-weight: bold;
            font-size: 14px;
            color: #d63384;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
        }
        
        .info-row {
            margin-bottom: 8px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .info-row strong {
            color: #333;
            min-width: 120px;
        }
        
        .highlight {
            background: #fff3cd;
            padding: 2px 6px;
            border-radius: 4px;
            font-weight: bold;
        }
        
        .items-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            font-size: 12px;
        }
        
        .items-table th {
            background: #d63384;
            color: white;
            padding: 12px 8px;
            text-align: center;
            font-weight: bold;
            border: 1px solid #d63384;
        }
        
        .items-table td {
            padding: 10px 8px;
            border: 1px solid #ddd;
            text-align: center;
        }
        
        .items-table tbody tr:nth-child(even) {
            background: #f8f9fa;
        }
        
        .items-table tbody tr:hover {
            background: #e9ecef;
        }
        
        .text-left { text-align: left !important; }
        .text-right { text-align: right !important; }
        
        .currency {
            font-weight: bold;
            color: #198754;
        }
        
        .total-section {
            margin-top: 20px;
            text-align: right;
        }
        
        .total-row {
            display: flex;
            justify-content: flex-end;
            margin-bottom: 8px;
            font-size: 14px;
        }
        
        .total-label {
            min-width: 150px;
            text-align: right;
            padding-right: 20px;
            font-weight: bold;
        }
        
        .total-value {
            min-width: 120px;
            text-align: right;
            font-weight: bold;
            color: #198754;
        }
        
        .grand-total {
            border-top: 2px solid #333;
            padding-top: 10px;
            font-size: 16px;
            color: #d63384;
        }
        
        .delivery-footer {
            margin-top: 40px;
            page-break-inside: avoid;
        }
        
        .signature-section {
            display: flex;
            justify-content: space-between;
            margin-top: 40px;
            gap: 50px;
        }
        
        .signature-box {
            flex: 1;
            text-align: center;
            min-height: 100px;
        }
        
        .signature-title {
            font-weight: bold;
            margin-bottom: 60px;
            color: #333;
        }
        
        .signature-line {
            border-top: 1px solid #333;
            padding-top: 5px;
            font-style: italic;
            color: #666;
        }
        
        .notes-section {
            margin-top: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #ffc107;
        }
        
        .notes-section h4 {
            margin: 0 0 10px 0;
            color: #856404;
        }
        
        .notes-section p {
            margin: 0;
            line-height: 1.6;
        }
    </style>
</head>
<body>
    <div class="delivery-container">
        <!-- Header -->
        <div class="delivery-header">
            <div class="company-info">
                <div class="company-name">{{CompanyName}}</div>
                <div class="company-details">
                    <div>{{CompanyAddress}}</div>
                    <div>ĐT: {{CompanyPhone}} | Email: {{CompanyEmail}}</div>
                    <div>MST: {{CompanyTaxCode}}</div>
                </div>
            </div>
            <h1 class="delivery-title">PHIẾU GIAO HÀNG</h1>
            <div class="delivery-number">{{DeliveryNoteCode}}</div>
        </div>

        <!-- Delivery and customer details -->
        <div class="delivery-details">
            <div class="delivery-info">
                <div class="info-label">📋 Thông tin phiếu giao hàng</div>
                <div class="info-row">
                    <strong>Số phiếu:</strong>
                    <span class="highlight">{{DeliveryNoteCode}}</span>
                </div>
                <div class="info-row">
                    <strong>Ngày giao:</strong>
                    <span>{{DeliveryDate}}</span>
                </div>
                <div class="info-row">
                    <strong>Mã đơn hàng:</strong>
                    <span>{{PurchaseOrderCode}}</span>
                </div>
            </div>
            
            <div class="customer-info">
                <div class="info-label">👤 Thông tin khách hàng</div>
                <div class="info-row">
                    <strong>Tên:</strong>
                    <span>{{CustomerName}}</span>
                </div>
                <div class="info-row">
                    <strong>Địa chỉ:</strong>
                    <span>{{CustomerAddress}}</span>
                </div>
                <div class="info-row">
                    <strong>Điện thoại:</strong>
                    <span>{{CustomerPhone}}</span>
                </div>
            </div>
        </div>

        <!-- Items table -->
        <table class="items-table">
            <thead>
                <tr>
                    <th style="width: 10%;">STT</th>
                    <th style="width: 60%;">Tên sản phẩm</th>
                    <th style="width: 15%;">Số lượng</th>
                    <th style="width: 15%;">Đơn vị</th>
                </tr>
            </thead>
            <tbody>
                {{#Items}}
                <tr>
                    <td>{{STTItem}}</td>
                    <td class="text-left">{{ProductNameItem}}</td>
                    <td>{{QuantityItem}}</td>
                    <td>{{UnitNameItem}}</td>
                </tr>
                {{/Items}}
            </tbody>
        </table>

        <!-- Footer -->
        <div class="delivery-footer">
            {{#Notes}}
            <div class="notes-section">
                <h4>📝 Ghi chú:</h4>
                <p>{{Notes}}</p>
            </div>
            {{/Notes}}
            
            <div class="signature-section">
                <div class="signature-box">
                    <div class="signature-title">👤 NGƯỜI NHẬN HÀNG</div>
                    <div class="signature-line">(Ký, ghi rõ họ tên)</div>
                </div>
                <div class="signature-box">
                    <div class="signature-title">🚚 NGƯỜI GIAO HÀNG</div>
                    <div class="signature-line">(Ký, ghi rõ họ tên)</div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
